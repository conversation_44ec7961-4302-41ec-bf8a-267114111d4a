CREATE TABLE `post_status_history` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`post_id` bigint unsigned NOT NULL,
	`old_status` varchar(20) NOT NULL,
	`new_status` varchar(20) NOT NULL,
	`changed_by` bigint unsigned NOT NULL,
	`changed_by_name` varchar(100) NOT NULL,
	`rejection_reason` text,
	`created_at` datetime NOT NULL,
	CONSTRAINT `post_status_history_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
DROP TABLE `trending_topics`;--> statement-breakpoint
ALTER TABLE `wikify1h_posts` MODIFY COLUMN `post_status` varchar(20) NOT NULL DEFAULT 'pending';--> statement-breakpoint
CREATE INDEX `post_idx` ON `post_status_history` (`post_id`);--> statement-breakpoint
CREATE INDEX `status_idx` ON `post_status_history` (`new_status`);--> statement-breakpoint
CREATE INDEX `created_idx` ON `post_status_history` (`created_at`);--> statement-breakpoint
CREATE INDEX `changed_by_idx` ON `post_status_history` (`changed_by`);