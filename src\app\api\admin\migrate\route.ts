import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { sql } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admins to run migration
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    console.log('🚀 Starting manual migration...');
    const results = [];

    // 1. Create post_status_history table
    try {
      console.log('📝 Creating post_status_history table...');
      await db.execute(sql`
        CREATE TABLE IF NOT EXISTS post_status_history (
          id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
          post_id BIGINT UNSIGNED NOT NULL,
          old_status VARCHAR(20) NOT NULL,
          new_status VARCHAR(20) NOT NULL,
          changed_by BIGINT UNSIGNED NOT NULL,
          changed_by_name VARCHAR(100) NOT NULL,
          rejection_reason TEXT NULL,
          created_at DATETIME NOT NULL,
          INDEX post_idx (post_id),
          INDEX status_idx (new_status),
          INDEX created_idx (created_at),
          INDEX changed_by_idx (changed_by)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      results.push('✅ post_status_history table created');
    } catch (error) {
      results.push(`⚠️ Table creation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 2. Check current post statuses
    try {
      console.log('📊 Checking current post statuses...');
      const currentStatuses = await db.execute(sql`
        SELECT post_status, COUNT(*) as count 
        FROM wikify1h_posts 
        WHERE post_type = 'post' 
        GROUP BY post_status
      `);
      
      results.push('📊 Current status distribution:');
      currentStatuses.forEach((row: any) => {
        results.push(`   ${row.post_status}: ${row.count} posts`);
      });
    } catch (error) {
      results.push(`⚠️ Status check: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 3. Update existing published posts to 'approved' status
    try {
      console.log('🔄 Converting published posts to approved status...');
      const updateResult = await db.execute(sql`
        UPDATE wikify1h_posts 
        SET post_status = 'approved' 
        WHERE post_status = 'publish' AND post_type = 'post'
      `);
      results.push(`✅ Updated ${updateResult.affectedRows || 0} posts from 'publish' to 'approved'`);
    } catch (error) {
      results.push(`⚠️ Post update: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 4. Create status history entries for converted posts
    try {
      console.log('📝 Creating status history for converted posts...');
      await db.execute(sql`
        INSERT INTO post_status_history (post_id, old_status, new_status, changed_by, changed_by_name, created_at)
        SELECT 
          ID as post_id,
          'publish' as old_status,
          'approved' as new_status,
          post_author as changed_by,
          'System Migration' as changed_by_name,
          NOW() as created_at
        FROM wikify1h_posts 
        WHERE post_status = 'approved' AND post_type = 'post'
      `);
      results.push('✅ Status history entries created');
    } catch (error) {
      results.push(`⚠️ History creation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 5. Update default post_status for new posts
    try {
      console.log('⚙️ Setting default post_status to pending...');
      await db.execute(sql`
        ALTER TABLE wikify1h_posts 
        ALTER COLUMN post_status SET DEFAULT 'pending'
      `);
      results.push('✅ Default post_status updated to pending');
    } catch (error) {
      results.push(`⚠️ Default update: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 6. Verify the changes
    try {
      console.log('🔍 Verifying migration results...');
      const finalStatuses = await db.execute(sql`
        SELECT post_status, COUNT(*) as count 
        FROM wikify1h_posts 
        WHERE post_type = 'post' 
        GROUP BY post_status
      `);
      
      results.push('📊 Final status distribution:');
      finalStatuses.forEach((row: any) => {
        results.push(`   ${row.post_status}: ${row.count} posts`);
      });

      const historyCount = await db.execute(sql`
        SELECT COUNT(*) as count FROM post_status_history
      `);
      results.push(`📚 Total status history entries: ${historyCount[0]?.count || 0}`);

      // 7. Show sample posts
      const samplePosts = await db.execute(sql`
        SELECT ID, post_title, post_status, post_date 
        FROM wikify1h_posts 
        WHERE post_type = 'post' 
        ORDER BY post_date DESC 
        LIMIT 5
      `);
      
      results.push('📄 Sample posts:');
      samplePosts.forEach((post: any) => {
        results.push(`   ${post.ID}: "${post.post_title}" - Status: ${post.post_status}`);
      });

    } catch (error) {
      results.push(`⚠️ Verification: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    results.push('');
    results.push('🎉 Migration completed!');
    results.push('');
    results.push('📋 Summary:');
    results.push('   ✅ post_status_history table created');
    results.push('   ✅ Published posts converted to approved status');
    results.push('   ✅ Status history tracking enabled');
    results.push('   ✅ Default status set to pending for new posts');

    return NextResponse.json({
      success: true,
      data: {
        message: 'Migration completed successfully',
        results: results
      }
    });

  } catch (error) {
    console.error('❌ Migration failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Migration failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
