import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { posts, users } from '@/lib/db/schema';
import { eq, and, inArray, desc, count, sql } from 'drizzle-orm';
import { createPostApprovedNotification, createPostRejectedNotification, createPostDeletedNotification } from '@/lib/utils/notifications';

// GET /api/admin/posts/moderate - Get pending posts for moderation
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Please login.' },
        { status: 401 }
      );
    }

    // Temporary: Allow all logged-in users to access moderation (for testing)
    // TODO: Restore admin-only access later
    // if (!['ADMIN', 'EDITOR'].includes(session.user.role)) {
    //   return NextResponse.json(
    //     { success: false, error: 'Unauthorized. Admin access required.' },
    //     { status: 401 }
    //   );
    // }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || 'pending';
    const search = searchParams.get('search');

    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = [
      eq(posts.post_type, 'post'),
      eq(posts.post_status, status)
    ];

    // Add search filter if provided
    if (search) {
      whereConditions.push(
        sql`(${posts.post_title} LIKE ${`%${search}%`} OR ${posts.post_content} LIKE ${`%${search}%`})`
      );
    }

    // Get posts with author information
    const result = await db
      .select({
        post: posts,
        author: users
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(and(...whereConditions))
      .orderBy(desc(posts.post_date))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(posts)
      .where(and(...whereConditions));

    // Format response
    const formattedPosts = result.map(({ post, author }) => ({
      id: post.ID,
      title: post.post_title,
      slug: post.post_name,
      excerpt: post.post_excerpt,
      content: post.post_content,
      status: post.post_status,
      date: post.post_date,
      modified: post.post_modified,
      author: author ? {
        id: author.ID,
        username: author.user_login,
        displayName: author.display_name,
        email: author.user_email
      } : null,
      commentCount: post.comment_count
    }));

    return NextResponse.json({
      success: true,
      data: {
        posts: formattedPosts,
        pagination: {
          page,
          limit,
          total: totalResult.count,
          totalPages: Math.ceil(totalResult.count / limit),
          hasMore: offset + formattedPosts.length < totalResult.count
        }
      }
    });

  } catch (error) {
    console.error('Error fetching posts for moderation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch posts for moderation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/posts/moderate - Moderate a post (approve/reject/delete)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Please login.' },
        { status: 401 }
      );
    }

    // Temporary: Allow all logged-in users to access moderation (for testing)
    // TODO: Restore admin-only access later

    const body = await request.json();
    const { postId, action, rejectionReason } = body;



    if (!postId || !action) {
      return NextResponse.json(
        { success: false, error: 'Post ID and action are required' },
        { status: 400 }
      );
    }

    if (!['approve', 'reject', 'delete'].includes(action)) {
      return NextResponse.json(
        { success: false, error: 'Invalid action. Must be approve, reject, or delete' },
        { status: 400 }
      );
    }

    if (action === 'reject' && !rejectionReason) {
      return NextResponse.json(
        { success: false, error: 'Rejection reason is required when rejecting a post' },
        { status: 400 }
      );
    }

    // Check if post exists
    const existingPost = await db
      .select()
      .from(posts)
      .where(eq(posts.ID, parseInt(postId)))
      .limit(1);

    if (!existingPost.length) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      );
    }

    const post = existingPost[0];
    const oldStatus = post.post_status;
    const now = new Date();
    const mysqlDateTime = now.toISOString().slice(0, 19).replace('T', ' ');

    if (action === 'delete') {
      // Send notification before deleting
      try {
        const adminName = session.user.name || session.user.username || 'Admin';
        const adminId = parseInt(session.user.id);

        await createPostDeletedNotification(
          post.post_author,
          parseInt(postId),
          post.post_title,
          adminId,
          adminName,
          rejectionReason || 'Post deleted by admin'
        );
      } catch (notificationError) {
        console.error('Error sending deletion notification:', notificationError);
        // Continue with deletion even if notification fails
      }

      // Delete the post
      await db
        .delete(posts)
        .where(eq(posts.ID, parseInt(postId)));

      // Create status history entry
      await db.execute(sql`
        INSERT INTO post_status_history (
          post_id, old_status, new_status, changed_by, changed_by_name, rejection_reason, created_at
        ) VALUES (
          ${parseInt(postId)}, ${oldStatus}, ${'deleted'}, ${parseInt(session.user.id)},
          ${session.user.name || session.user.username}, ${rejectionReason || 'Post deleted by admin'}, ${mysqlDateTime}
        )
      `);

      return NextResponse.json({
        success: true,
        data: {
          message: 'Post deleted successfully',
          postId: parseInt(postId),
          action: 'delete'
        }
      });
    }

    // For approve/reject actions
    const newStatus = action === 'approve' ? 'approved' : 'rejected';

    // Update post status
    await db
      .update(posts)
      .set({
        post_status: newStatus,
        post_modified: now,
        post_modified_gmt: now
      })
      .where(eq(posts.ID, parseInt(postId)));

    // Create status history entry
    await db.execute(sql`
      INSERT INTO post_status_history (
        post_id, old_status, new_status, changed_by, changed_by_name, rejection_reason, created_at
      ) VALUES (
        ${parseInt(postId)}, ${oldStatus}, ${newStatus}, ${parseInt(session.user.id)},
        ${session.user.name || session.user.username}, ${rejectionReason || null}, ${mysqlDateTime}
      )
    `);

    // Send notification to post author
    try {
      const adminName = session.user.name || session.user.username || 'Admin';
      const adminId = parseInt(session.user.id);

      if (action === 'approve') {
        await createPostApprovedNotification(
          post.post_author,
          parseInt(postId),
          post.post_title,
          adminId,
          adminName,
          post.post_name // post slug
        );
      } else if (action === 'reject') {
        await createPostRejectedNotification(
          post.post_author,
          parseInt(postId),
          post.post_title,
          adminId,
          adminName,
          rejectionReason || 'No specific reason provided'
        );
      }
    } catch (notificationError) {
      console.error('Error sending notification:', notificationError);
      // Don't fail the whole request if notification fails
    }

    return NextResponse.json({
      success: true,
      data: {
        message: `Post ${action}d successfully`,
        postId: parseInt(postId),
        action,
        newStatus,
        rejectionReason: rejectionReason || null
      }
    });

  } catch (error) {
    console.error('Error moderating post:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to moderate post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
