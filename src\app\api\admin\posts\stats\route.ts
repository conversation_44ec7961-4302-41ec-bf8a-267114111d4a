import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { posts } from '@/lib/db/schema';
import { eq, and, or, count, sql } from 'drizzle-orm';

// GET /api/admin/posts/stats - Get post moderation statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Please login.' },
        { status: 401 }
      );
    }

    // Temporary: Allow all logged-in users to access stats (for testing)
    // TODO: Restore admin-only access later

    // Get counts for different post statuses
    const [pendingCount] = await db
      .select({ count: count() })
      .from(posts)
      .where(and(
        eq(posts.post_type, 'post'),
        eq(posts.post_status, 'pending')
      ));

    const [approvedCount] = await db
      .select({ count: count() })
      .from(posts)
      .where(and(
        eq(posts.post_type, 'post'),
        or(eq(posts.post_status, 'approved'), eq(posts.post_status, 'publish'))
      ));

    const [rejectedCount] = await db
      .select({ count: count() })
      .from(posts)
      .where(and(
        eq(posts.post_type, 'post'),
        eq(posts.post_status, 'rejected')
      ));

    const [draftCount] = await db
      .select({ count: count() })
      .from(posts)
      .where(and(
        eq(posts.post_type, 'post'),
        eq(posts.post_status, 'draft')
      ));

    // Get recent activity (posts created in last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const sevenDaysAgoStr = sevenDaysAgo.toISOString().slice(0, 19).replace('T', ' ');

    const [recentPendingCount] = await db
      .select({ count: count() })
      .from(posts)
      .where(and(
        eq(posts.post_type, 'post'),
        eq(posts.post_status, 'pending'),
        sql`${posts.post_date} >= ${sevenDaysAgoStr}`
      ));

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          pending: pendingCount.count,
          approved: approvedCount.count,
          rejected: rejectedCount.count,
          draft: draftCount.count,
          total: pendingCount.count + approvedCount.count + rejectedCount.count + draftCount.count
        },
        recentActivity: {
          newPendingPosts: recentPendingCount.count
        }
      }
    });

  } catch (error) {
    console.error('Error fetching post moderation stats:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch post moderation stats',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
