import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, USER_STATUS } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { sendApprovalEmail, sendRejectionEmail } from '@/lib/utils/email';
import { createUserApprovedNotification, createUserRejectedNotification } from '@/lib/utils/notifications';

// PUT /api/admin/users/[id]/approve - Approve user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);

    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Parse request body for ads permission
    const body = await request.json().catch(() => ({}));
    const { canInsertAds = false } = body;

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (!existingUser.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Update user status to approved
    await db
      .update(users)
      .set({ user_status: USER_STATUS.APPROVED })
      .where(eq(users.ID, userId));

    // Set ads permission if specified
    if (typeof canInsertAds === 'boolean') {
      const { setAdsPermission } = await import('@/lib/utils/ads-permissions');
      await setAdsPermission(userId, canInsertAds);
    }

    // Send notification and email to the user
    try {
      const adminName = session.user.name || session.user.username || 'Admin';
      const adminId = parseInt(session.user.id);

      await createUserApprovedNotification(
        userId,
        adminId,
        adminName,
        canInsertAds
      );
    } catch (notificationError) {
      console.error('User approval notification error:', notificationError);
      // Don't fail the approval if notification fails, just log it
    }

    return NextResponse.json({
      success: true,
      message: 'User approved successfully with notification and email sent',
      data: {
        id: userId,
        status: 'approved',
        canInsertAds,
        username: existingUser[0].user_login,
        email: existingUser[0].user_email
      }
    });

  } catch (error) {
    console.error('Error approving user:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to approve user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users/[id]/approve - Reject user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);

    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.ID, userId))
      .limit(1);

    if (!existingUser.length) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Update user status to rejected
    await db
      .update(users)
      .set({ user_status: USER_STATUS.REJECTED })
      .where(eq(users.ID, userId));

    // Parse request body for rejection reason
    const body = await request.json().catch(() => ({}));
    const { rejectionReason = 'No specific reason provided' } = body;

    // Send notification and email to the user
    try {
      const adminName = session.user.name || session.user.username || 'Admin';
      const adminId = parseInt(session.user.id);

      await createUserRejectedNotification(
        userId,
        adminId,
        adminName,
        rejectionReason
      );
    } catch (notificationError) {
      console.error('User rejection notification error:', notificationError);
      // Don't fail the rejection if notification fails, just log it
    }

    return NextResponse.json({
      success: true,
      message: 'User rejected successfully with notification and email sent',
      data: {
        id: userId,
        status: 'rejected',
        username: existingUser[0].user_login,
        email: existingUser[0].user_email,
        rejectionReason
      }
    });

  } catch (error) {
    console.error('Error rejecting user:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to reject user',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
