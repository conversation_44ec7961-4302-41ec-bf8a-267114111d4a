import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { posts, users, term_relationships, term_taxonomy, postmeta } from '@/lib/db/schema';
import { eq, and, inArray, sql } from 'drizzle-orm';
import { processWordPressContent, generateExcerpt, getFeaturedImage, extractImagesFromContent } from '@/lib/utils/content-processor';
import { generateUniquePostSlug } from '@/lib/utils/slug-server';
import { deleteImage, extractPublicId, deleteMultipleImages } from '@/lib/cloudinary';
import { createPostDeletedNotification, createPostEditedNotification } from '@/lib/utils/notifications';
import { hasPostEditPermission, hasPostDeletePermission } from '@/lib/utils/post-permissions';

/**
 * Delete images from Cloudinary and handle errors gracefully
 */
async function deleteImagesFromCloudinary(imageUrls: string[], userId: string): Promise<{ successful: number; failed: number; total: number }> {
  // Extract public IDs from URLs
  const publicIds: string[] = [];

  for (const imageUrl of imageUrls) {
    try {
      const publicId = extractPublicId(imageUrl);
      if (!publicId) continue;

      // Check if user owns the image (basic security check)
      if (!publicId.includes(userId)) {
        console.warn(`Skipping deletion of image ${publicId} - user ${userId} doesn't own it`);
        continue;
      }

      publicIds.push(publicId);
    } catch (error) {
      console.error(`Error processing image URL ${imageUrl}:`, error);
    }
  }

  if (publicIds.length === 0) {
    return { successful: 0, failed: 0, total: 0 };
  }

  // Delete images from Cloudinary
  const deleteResult = await deleteMultipleImages(publicIds);

  console.log(`Cloudinary deletion result: ${deleteResult.successful}/${deleteResult.total} images deleted successfully`);

  return {
    successful: deleteResult.successful,
    failed: deleteResult.failed,
    total: deleteResult.total
  };
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params for Next.js 15 compatibility
    const { id } = await params;
    const postId = parseInt(id);

    if (isNaN(postId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid post ID' },
        { status: 400 }
      );
    }

    const result = await db
      .select({
        post: posts,
        author: users
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(eq(posts.ID, postId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      );
    }

    const { post, author } = result[0];

    // Get post categories
    const postCategories = await db.execute(sql`
      SELECT t.term_id, t.name, t.slug
      FROM wikify1h_terms t
      INNER JOIN wikify1h_term_taxonomy tt ON t.term_id = tt.term_id
      INNER JOIN wikify1h_term_relationships tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
      WHERE tr.object_id = ${post.ID} AND tt.taxonomy = 'category'
    `);

    const categories = postCategories.map((cat: any) => cat.term_id);

    // Get ad codes from postmeta
    const adMeta = await db.execute(sql`
      SELECT meta_key, meta_value
      FROM wikify1h_postmeta
      WHERE post_id = ${post.ID}
      AND meta_key IN ('_before_content_ads', '_after_content_ads')
    `);

    // Extract the actual data from the result array
    const adMetaData = Array.isArray(adMeta[0]) ? adMeta[0] : adMeta;

    const beforeContentAds = adMetaData.find((meta: any) => meta.meta_key === '_before_content_ads')?.meta_value || '';
    const afterContentAds = adMetaData.find((meta: any) => meta.meta_key === '_after_content_ads')?.meta_value || '';

    return NextResponse.json({
      success: true,
      data: {
        id: post.ID,
        title: post.post_title,
        slug: post.post_name,
        excerpt: post.post_excerpt || generateExcerpt(post.post_content),
        content: processWordPressContent(post.post_content),
        status: post.post_status,
        date: post.post_date,
        modified: post.post_modified,
        featuredImage: await getFeaturedImage(post.ID, post.post_content),
        author: author ? {
          id: author.ID,
          username: author.user_login,
          displayName: author.display_name,
          email: author.user_email
        } : null,
        commentCount: post.comment_count,
        categories: categories,
        beforeContentAds: beforeContentAds,
        afterContentAds: afterContentAds
      }
    });

  } catch (error) {
    console.error('Error fetching post:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await params for Next.js 15 compatibility
    const { id } = await params;
    const postId = parseInt(id);
    const body = await request.json();
    const { title, content, excerpt, status, categories, featuredImage, beforeContentAds, afterContentAds } = body;

    if (isNaN(postId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid post ID' },
        { status: 400 }
      );
    }

    // Check if post exists and user has permission
    const existingPost = await db
      .select()
      .from(posts)
      .where(eq(posts.ID, postId))
      .limit(1);

    if (!existingPost.length) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      );
    }

    // Check if user has permission to edit this post
    const hasPermission = hasPostEditPermission(
      existingPost[0].post_author,
      parseInt(session.user.id),
      session.user.role
    );

    if (!hasPermission) {
      return NextResponse.json(
        { success: false, error: 'You do not have permission to edit this post. Only the post author or administrators can edit posts.' },
        { status: 403 }
      );
    }

    // For notification purposes
    const isOwner = existingPost[0].post_author === parseInt(session.user.id);
    const isAdminOrEditor = ['ADMIN', 'EDITOR'].includes(session.user.role);

    // Create current datetime as Date object
    const now = new Date();

    // Update post
    const updateData: any = {
      post_modified: now,
      post_modified_gmt: now
    };

    if (title) updateData.post_title = title;
    if (content) updateData.post_content = content;
    if (excerpt !== undefined) updateData.post_excerpt = excerpt;

    // Handle status changes with approval system
    let actualStatus = status;
    const isAdmin = session.user.role === 'ADMIN' || session.user.role === 'EDITOR';
    const oldStatus = existingPost[0].post_status;

    if (status) {
      // If user tries to publish but is not admin, set to pending
      if (status === 'publish' && !isAdmin) {
        actualStatus = 'pending';
      }

      // If post was rejected and user is editing, set back to pending
      if (oldStatus === 'rejected' && !isAdmin) {
        actualStatus = 'pending';
      }

      // If existing published post is being edited by non-admin, set to pending
      if ((oldStatus === 'publish' || oldStatus === 'approved') && !isAdmin && status === 'publish') {
        actualStatus = 'pending';
      }

      updateData.post_status = actualStatus;
    }

    // Generate new unique slug if title changed
    if (title) {
      updateData.post_name = await generateUniquePostSlug(title, postId);
    }

    await db
      .update(posts)
      .set(updateData)
      .where(eq(posts.ID, postId));

    // Create status history entry if status changed
    if (status && actualStatus !== oldStatus) {
      try {
        const mysqlDateTime = now.toISOString().slice(0, 19).replace('T', ' ');
        await db.execute(sql`
          INSERT INTO post_status_history (
            post_id, old_status, new_status, changed_by, changed_by_name, created_at
          ) VALUES (
            ${postId}, ${oldStatus}, ${actualStatus}, ${parseInt(session.user.id)},
            ${session.user.name || session.user.username}, ${mysqlDateTime}
          )
        `);
      } catch (historyError) {
        console.error('Error saving post status history:', historyError);
        // Don't fail the whole request if history save fails
      }
    }

    // Update featured image if provided
    if (featuredImage !== undefined) {
      try {
        // First, remove existing featured image meta
        await db.execute(sql`
          DELETE FROM wikify1h_postmeta
          WHERE post_id = ${postId} AND meta_key = '_thumbnail_url'
        `);

        // Then add new featured image meta if provided
        if (featuredImage) {
          await db.execute(sql`
            INSERT INTO wikify1h_postmeta (post_id, meta_key, meta_value)
            VALUES (${postId}, '_thumbnail_url', ${featuredImage})
          `);
        }
      } catch (imageError) {
        console.error('Error updating featured image:', imageError);
        // Don't fail the whole request if image update fails
      }
    }

    // Update ad codes if provided
    if (beforeContentAds !== undefined || afterContentAds !== undefined) {
      try {
        // Update before content ads
        if (beforeContentAds !== undefined) {
          // First delete existing meta
          await db.execute(sql`
            DELETE FROM wikify1h_postmeta
            WHERE post_id = ${postId} AND meta_key = '_before_content_ads'
          `);

          // Insert new meta if value is not empty
          if (beforeContentAds) {
            await db.execute(sql`
              INSERT INTO wikify1h_postmeta (post_id, meta_key, meta_value)
              VALUES (${postId}, '_before_content_ads', ${beforeContentAds})
            `);
          }
        }

        // Update after content ads
        if (afterContentAds !== undefined) {
          // First delete existing meta
          await db.execute(sql`
            DELETE FROM wikify1h_postmeta
            WHERE post_id = ${postId} AND meta_key = '_after_content_ads'
          `);

          // Insert new meta if value is not empty
          if (afterContentAds) {
            await db.execute(sql`
              INSERT INTO wikify1h_postmeta (post_id, meta_key, meta_value)
              VALUES (${postId}, '_after_content_ads', ${afterContentAds})
            `);
          }
        }
      } catch (adError) {
        console.error('Error updating ad meta:', adError);
        // Don't fail the whole request if ad update fails
      }
    }

    // Update categories if provided
    if (categories !== undefined) {
      try {
        // First, remove existing category relationships
        await db.execute(sql`
          DELETE FROM wikify1h_term_relationships
          WHERE object_id = ${postId}
          AND term_taxonomy_id IN (
            SELECT term_taxonomy_id FROM wikify1h_term_taxonomy
            WHERE taxonomy = 'category'
          )
        `);

        // Then add new category relationships
        if (categories && categories.length > 0) {
          const categoryTaxonomies = await db
            .select({ term_taxonomy_id: term_taxonomy.term_taxonomy_id })
            .from(term_taxonomy)
            .where(and(
              eq(term_taxonomy.taxonomy, 'category'),
              inArray(term_taxonomy.term_id, categories)
            ));

          for (const taxonomy of categoryTaxonomies) {
            await db.execute(sql`
              INSERT INTO wikify1h_term_relationships (object_id, term_taxonomy_id, term_order)
              VALUES (${postId}, ${taxonomy.term_taxonomy_id}, 0)
            `);
          }
        }
      } catch (categoryError) {
        console.error('Error updating categories:', categoryError);
        // Don't fail the whole request if category update fails
      }
    }

    // Create notification if admin is editing another user's post
    if (!isOwner && isAdminOrEditor) {
      const adminName = session.user.name || session.user.username || 'Admin';
      await createPostEditedNotification(
        existingPost[0].post_author,
        postId,
        existingPost[0].post_title,
        parseInt(session.user.id),
        adminName
      );
    }

    return NextResponse.json({
      success: true,
      data: { id: postId, ...updateData, categories, featuredImage }
    });

  } catch (error) {
    console.error('Error updating post:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await params for Next.js 15 compatibility
    const { id } = await params;
    const postId = parseInt(id);

    if (isNaN(postId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid post ID' },
        { status: 400 }
      );
    }

    // Check if post exists and user has permission
    const existingPost = await db
      .select()
      .from(posts)
      .where(eq(posts.ID, postId))
      .limit(1);

    if (!existingPost.length) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      );
    }

    // Check if user has permission to delete this post
    const hasPermission = hasPostDeletePermission(
      existingPost[0].post_author,
      parseInt(session.user.id),
      session.user.role
    );

    if (!hasPermission) {
      return NextResponse.json(
        { success: false, error: 'You do not have permission to delete this post. Only the post author or administrators can delete posts.' },
        { status: 403 }
      );
    }

    // For notification purposes
    const isOwner = existingPost[0].post_author === parseInt(session.user.id);
    const isAdminOrEditor = ['ADMIN', 'EDITOR'].includes(session.user.role);

    const post = existingPost[0];
    const imagesToDelete: string[] = [];

    // 1. Get featured image from postmeta
    try {
      const featuredImageMeta = await db
        .select()
        .from(postmeta)
        .where(
          and(
            eq(postmeta.post_id, postId),
            eq(postmeta.meta_key, '_thumbnail_url')
          )
        )
        .limit(1);

      if (featuredImageMeta.length > 0 && featuredImageMeta[0].meta_value) {
        imagesToDelete.push(featuredImageMeta[0].meta_value);
      }
    } catch (error) {
      console.error('Error fetching featured image meta:', error);
    }

    // 2. Extract images from post content
    try {
      const contentImages = extractImagesFromContent(post.post_content);
      imagesToDelete.push(...contentImages);
    } catch (error) {
      console.error('Error extracting images from content:', error);
    }

    // 3. Delete images from Cloudinary (only Cloudinary images)
    const cloudinaryImages = imagesToDelete.filter(url =>
      url && url.includes('cloudinary.com')
    );

    let deletionResult = { successful: 0, failed: 0, total: 0 };
    if (cloudinaryImages.length > 0) {
      console.log(`Deleting ${cloudinaryImages.length} images from Cloudinary for post ${postId}`);
      deletionResult = await deleteImagesFromCloudinary(cloudinaryImages, session.user.id);
    }

    // 4. Delete post metadata from database
    try {
      await db
        .delete(postmeta)
        .where(eq(postmeta.post_id, postId));
      console.log(`Deleted postmeta for post ${postId}`);
    } catch (error) {
      console.error('Error deleting postmeta:', error);
    }

    // 5. Delete term relationships
    try {
      await db
        .delete(term_relationships)
        .where(eq(term_relationships.object_id, postId));
      console.log(`Deleted term relationships for post ${postId}`);
    } catch (error) {
      console.error('Error deleting term relationships:', error);
    }

    // 6. Create notification if admin is deleting another user's post
    if (!isOwner && isAdminOrEditor) {
      const adminName = session.user.name || session.user.username || 'Admin';
      await createPostDeletedNotification(
        existingPost[0].post_author,
        postId,
        existingPost[0].post_title,
        parseInt(session.user.id),
        adminName
      );
    }

    // 7. Finally, delete the post itself (hard delete instead of soft delete)
    await db
      .delete(posts)
      .where(eq(posts.ID, postId));

    console.log(`Successfully deleted post ${postId} and associated data`);

    return NextResponse.json({
      success: true,
      message: 'Post and associated images deleted successfully',
      details: {
        postId: postId,
        totalImages: imagesToDelete.length,
        cloudinaryImages: cloudinaryImages.length,
        imagesDeleted: deletionResult.successful,
        imagesFailed: deletionResult.failed
      }
    });

  } catch (error) {
    console.error('Error deleting post:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
