import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { posts, users } from '@/lib/db/schema';
import { eq, and, or, sql } from 'drizzle-orm';
import { processWordPressContent, generateExcerpt, getFeaturedImage } from '@/lib/utils/content-processor';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { slug } = await params;

    if (!slug) {
      return NextResponse.json(
        { success: false, error: 'Slug is required' },
        { status: 400 }
      );
    }

    // Determine what posts the user can see
    const isAdmin = session?.user?.role === 'ADMIN' || session?.user?.role === 'EDITOR';
    const currentUserId = session?.user?.id ? parseInt(session.user.id) : null;

    let statusConditions;
    if (isAdmin) {
      // <PERSON><PERSON> can see all posts except deleted
      statusConditions = or(
        eq(posts.post_status, 'approved'),
        eq(posts.post_status, 'pending'),
        eq(posts.post_status, 'rejected'),
        eq(posts.post_status, 'draft')
      );
    } else if (currentUserId) {
      // Logged-in users can see approved posts + their own posts in any status
      statusConditions = or(
        eq(posts.post_status, 'approved'),
        and(
          eq(posts.post_author, currentUserId),
          or(
            eq(posts.post_status, 'pending'),
            eq(posts.post_status, 'rejected'),
            eq(posts.post_status, 'draft')
          )
        )
      );
    } else {
      // Public users can only see approved and published posts
      statusConditions = or(
        eq(posts.post_status, 'approved'),
        eq(posts.post_status, 'publish')
      );
    }

    const result = await db
      .select({
        post: posts,
        author: users
      })
      .from(posts)
      .leftJoin(users, eq(posts.post_author, users.ID))
      .where(and(
        eq(posts.post_name, slug),
        eq(posts.post_type, 'post'),
        statusConditions
      ))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      );
    }

    const { post, author } = result[0];

    // Get ad codes from postmeta
    const adMeta = await db.execute(sql`
      SELECT meta_key, meta_value
      FROM wikify1h_postmeta
      WHERE post_id = ${post.ID}
      AND meta_key IN ('_before_content_ads', '_after_content_ads')
    `);

    // Extract the actual data from the result array
    const adMetaData = Array.isArray(adMeta[0]) ? adMeta[0] : adMeta;

    const beforeContentAds = adMetaData.find((meta: any) => meta.meta_key === '_before_content_ads')?.meta_value || '';
    const afterContentAds = adMetaData.find((meta: any) => meta.meta_key === '_after_content_ads')?.meta_value || '';

    const formattedResult = {
      post: {
        ...post,
        post_content: processWordPressContent(post.post_content),
        post_excerpt: post.post_excerpt || generateExcerpt(post.post_content),
        featured_image: await getFeaturedImage(post.ID, post.post_content),
        beforeContentAds: beforeContentAds,
        afterContentAds: afterContentAds
      },
      author
    };

    return NextResponse.json({
      success: true,
      data: formattedResult
    });

  } catch (error) {
    console.error('Error fetching post by slug:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
