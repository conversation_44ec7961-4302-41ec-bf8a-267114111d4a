import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createPostApprovedNotification, createPostRejectedNotification } from '@/lib/utils/notifications';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, postId = 1, postTitle = 'Test Post', rejectionReason } = body;

    const adminId = parseInt(session.user.id);
    const adminName = session.user.name || session.user.username || 'Admin';
    const userId = parseInt(session.user.id); // Self-test

    let result = false;

    if (type === 'approved') {
      result = await createPostApprovedNotification(
        userId,
        postId,
        postTitle,
        adminId,
        adminName,
        'test-post-slug'
      );
    } else if (type === 'rejected') {
      result = await createPostRejectedNotification(
        userId,
        postId,
        postTitle,
        adminId,
        adminName,
        rejectionReason || 'This is a test rejection'
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        message: `Test ${type} notification sent`,
        notificationSent: result,
        emailSent: 'Check server console for email logs',
        type,
        postId,
        postTitle,
        adminName
      }
    });

  } catch (error) {
    console.error('Test notification error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to send test notification',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
