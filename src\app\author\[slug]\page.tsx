'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Layout from '@/components/layout/layout';
import PostCard from '@/components/blog/post-card';

interface Author {
  id: number;
  username: string;
  displayName: string;
  nicename: string;
  email: string;
  url: string;
  bio: string;
  registered: string;
  role: string;
  stats: {
    posts: number;
  };
  recentPosts: {
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    date: string;
    commentCount: number;
  }[];
}

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  date: string;
  featured_image?: string;
  commentCount?: number;
  author?: {
    id: number;
    username: string;
    displayName: string;
  };
}

export default function AuthorProfilePage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [author, setAuthor] = useState<Author | null>(null);
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [postsLoading, setPostsLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (slug) {
      fetchAuthor();
    }
  }, [slug]);

  useEffect(() => {
    if (author) {
      fetchAuthorPosts(1, true);
    }
  }, [author]);

  const fetchAuthor = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/authors/${slug}`);
      const data = await response.json();

      if (data.success) {
        setAuthor(data.data);
      } else {
        setError(data.error || 'Author not found');
      }
    } catch (error) {
      console.error('Error fetching author:', error);
      setError('Failed to load author profile');
    } finally {
      setLoading(false);
    }
  };

  const fetchAuthorPosts = async (page: number = 1, reset: boolean = false) => {
    try {
      if (reset) {
        setPostsLoading(true);
      }

      // Use author ID for filtering posts, not username
      const authorId = author?.id;
      if (!authorId) {
        setPostsLoading(false);
        return;
      }

      const response = await fetch(`/api/posts?author=${authorId}&page=${page}&limit=6&status=approved,publish`);
      const data = await response.json();

      if (data.success) {
        if (reset) {
          setPosts(data.data);
        } else {
          // Filter out duplicates by checking if post ID already exists
          setPosts(prev => {
            const existingIds = new Set(prev.map(post => post.id));
            const newPosts = data.data.filter(post => !existingIds.has(post.id));
            return [...prev, ...newPosts];
          });
        }
        setHasMore(data.pagination.hasMore);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('Error fetching author posts:', error);
    } finally {
      setPostsLoading(false);
    }
  };

  const loadMorePosts = () => {
    if (hasMore && !postsLoading) {
      fetchAuthorPosts(currentPage + 1, false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'text-red-600 bg-red-100 border-red-200';
      case 'EDITOR': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'AUTHOR': return 'text-green-600 bg-green-100 border-green-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getGradientForRole = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'from-red-400 to-pink-500';
      case 'EDITOR': return 'from-blue-400 to-indigo-500';
      case 'AUTHOR': return 'from-green-400 to-emerald-500';
      default: return 'from-gray-400 to-slate-500';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
          {/* Loading Header */}
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 relative overflow-hidden">
            <div className="absolute inset-0 bg-black opacity-20"></div>
            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
              <div className="animate-pulse">
                <div className="flex flex-col lg:flex-row items-start lg:items-center space-y-8 lg:space-y-0 lg:space-x-12">
                  <div className="w-40 h-40 bg-white/20 rounded-full"></div>
                  <div className="flex-1 space-y-4">
                    <div className="h-8 bg-white/20 rounded-lg w-64"></div>
                    <div className="h-4 bg-white/20 rounded-lg w-32"></div>
                    <div className="h-4 bg-white/20 rounded-lg w-96"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Loading Content */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
                  <div className="h-48 bg-gray-200"></div>
                  <div className="p-6 space-y-4">
                    <div className="h-6 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !author) {
    return (
      <Layout>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-full mb-6">
              <svg className="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Author Not Found</h1>
            <p className="text-gray-600 mb-8 text-lg">{error || 'The author you are looking for does not exist.'}</p>
            <Link href="/author">
              <button className="bg-indigo-600 text-white px-8 py-3 rounded-xl hover:bg-indigo-700 transition-colors font-semibold">
                View All Authors
              </button>
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Enhanced Author Header */}
        <div className="relative overflow-hidden">
          {/* Background with gradient */}
          <div className={`bg-gradient-to-r ${getGradientForRole(author.role)} relative`}>
            <div className="absolute inset-0 bg-black opacity-20"></div>

            {/* Decorative elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute -top-20 -right-20 w-64 h-64 bg-white opacity-10 rounded-full"></div>
              <div className="absolute top-32 -left-20 w-48 h-48 bg-white opacity-10 rounded-full"></div>
              <div className="absolute bottom-10 right-1/4 w-32 h-32 bg-white opacity-10 rounded-full"></div>
            </div>

            {/* Grid pattern overlay */}
            <div className="absolute inset-0">
              <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                <defs>
                  <pattern id="authorGrid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" strokeWidth="0.5"/>
                  </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#authorGrid)" />
              </svg>
            </div>

            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
              <div className="flex flex-col lg:flex-row items-start lg:items-center space-y-8 lg:space-y-0 lg:space-x-12">
                {/* Enhanced Avatar */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    <div className="w-40 h-40 rounded-full bg-white p-2 shadow-2xl">
                      <div className={`w-full h-full rounded-full bg-gradient-to-br ${getGradientForRole(author.role)} flex items-center justify-center text-4xl font-bold text-white shadow-inner`}>
                        {author.displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </div>
                    </div>
                    {/* Online indicator */}
                    <div className="absolute bottom-4 right-4 w-8 h-8 bg-green-400 rounded-full border-4 border-white shadow-lg"></div>

                  </div>
                </div>

                {/* Author Info */}
                <div className="flex-1 text-white">
                  <div className="mb-6">
                    <h1 className="text-5xl font-bold mb-2">{author.displayName}</h1>
                    <p className="text-xl opacity-90 mb-4">@{author.username}</p>
                  </div>

                  {/* Bio */}
                  {author.bio && (
                    <p className="text-xl leading-relaxed opacity-90 mb-6 max-w-3xl">
                      {author.bio}
                    </p>
                  )}


                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Author Posts Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Section Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full mb-6">
              <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Latest Articles by {author.displayName}
            </h2>

          </div>

          {/* Posts Grid */}
          {postsLoading && posts.length === 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
                  <div className="h-48 bg-gray-200"></div>
                  <div className="p-6 space-y-4">
                    <div className="h-6 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-16">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-6">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">No Posts Yet</h3>
              <p className="text-gray-600 mb-8">
                {author.displayName} hasn't published any posts yet. Check back later!
              </p>
              <Link href="/blog">
                <button className="bg-indigo-600 text-white px-8 py-3 rounded-xl hover:bg-indigo-700 transition-colors font-semibold">
                  Explore All Posts
                </button>
              </Link>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                {posts.map((post) => (
                  <PostCard key={post.id} post={post} showExcerpt={false} />
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="text-center">
                  <button
                    onClick={loadMorePosts}
                    disabled={postsLoading}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 font-semibold transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {postsLoading ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Loading...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <span>Load More Posts</span>
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    )}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Layout>
  );
}
