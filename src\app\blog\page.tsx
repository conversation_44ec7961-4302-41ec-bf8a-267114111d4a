'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Layout from '@/components/layout/layout';
import PostCard from '@/components/blog/post-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  date: string;
  featured_image?: string;
  author?: {
    id: number;
    username: string;
    displayName: string;
  };
  commentCount: number;
}



interface BlogResponse {
  success: boolean;
  data: BlogPost[];
  pagination: {
    page: number;
    limit: number;
    hasMore: boolean;
  };
}

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [totalPosts, setTotalPosts] = useState(0);

  const searchParams = useSearchParams();
  const router = useRouter();

  const handlePostDeleted = () => {
    // Refresh the posts list
    fetchPosts(1, true, searchQuery);
  };

  useEffect(() => {
    // Get search query from URL params
    const search = searchParams.get('search') || '';
    setSearchQuery(search);
    setSearchInput(search);

    // Reset and fetch posts
    fetchPosts(1, true, search);
  }, [searchParams]);

  const fetchPosts = async (page: number = 1, reset: boolean = false, search: string = '') => {
    try {
      if (reset) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const params = new URLSearchParams();
      params.set('page', page.toString());
      params.set('limit', '12');
      params.set('status', 'approved,publish');

      if (search.trim()) {
        params.set('search', search.trim());
      }

      const response = await fetch(`/api/posts?${params.toString()}`);
      const result: BlogResponse = await response.json();

      if (result.success) {
        if (reset) {
          setPosts(result.data);
        } else {
          // Filter out duplicates by checking if post ID already exists
          setPosts(prevPosts => {
            const existingIds = new Set(prevPosts.map(post => post.id));
            const newPosts = result.data.filter(post => !existingIds.has(post.id));
            return [...prevPosts, ...newPosts];
          });
        }
        setHasMore(result.pagination.hasMore);
        setCurrentPage(page);
        
        // For total count, we'll estimate based on current results
        if (reset) {
          setTotalPosts(result.data.length + (result.pagination.hasMore ? 50 : 0));
        }
      } else {
        console.error('Failed to fetch posts:', result);
        if (reset) {
          setPosts([]);
          setTotalPosts(0);
        }
      }
    } catch (error) {
      console.error('Error fetching posts:', error);
      if (reset) {
        setPosts([]);
        setTotalPosts(0);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedSearch = searchInput.trim();

    // Update URL with search params
    const params = new URLSearchParams();
    if (trimmedSearch) {
      params.set('search', trimmedSearch);
    }

    const newUrl = params.toString() ? `/blog?${params.toString()}` : '/blog';
    router.push(newUrl);
  };

  const clearSearch = () => {
    setSearchInput('');
    router.push('/blog');
  };

  const loadMore = () => {
    if (!loadingMore && hasMore) {
      fetchPosts(currentPage + 1, false, searchQuery);
    }
  };

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white py-20 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-purple-500/20 rounded-full blur-xl animate-float"></div>
        <div className="absolute top-32 right-20 w-16 h-16 bg-blue-500/20 rounded-full blur-xl animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-pink-500/20 rounded-full blur-xl animate-float" style={{ animationDelay: '4s' }}></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent leading-tight">
            Explore Our Blog
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Discover insightful articles, tutorials, and stories from our community of writers
          </p>
          
          {/* Search Form */}
          <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search articles..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                className="w-full px-6 py-4 text-lg rounded-2xl border-0 bg-white/10 backdrop-blur-sm text-white placeholder-gray-300 focus:bg-white/20 transition-all duration-300"
              />
              <div className="absolute right-2 top-2 flex items-center space-x-2">
                {searchInput && (
                  <Button
                    type="button"
                    onClick={clearSearch}
                    size="sm"
                    className="bg-white/20 hover:bg-white/30 text-white border-0 rounded-xl"
                  >
                    Clear
                  </Button>
                )}
                <Button
                  type="submit"
                  size="sm"
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 rounded-xl px-6"
                >
                  Search
                </Button>
              </div>
            </div>
          </form>
        </div>
      </section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Results Header */}
        {searchQuery && (
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                  Search Results for "{searchQuery}"
                </h2>
                <p className="text-gray-600">
                  {loading ? 'Loading...' : `${posts.length} article${posts.length !== 1 ? 's' : ''} found`}
                </p>
              </div>
              <Button
                onClick={clearSearch}
                variant="outline"
                className="mt-4 sm:mt-0"
              >
                View All Articles
              </Button>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading articles...</p>
            </div>
          </div>
        )}

        {/* No Results */}
        {!loading && posts.length === 0 && (
          <Card className="relative overflow-hidden border-2 border-dashed border-gray-300 bg-gradient-to-br from-gray-50 to-white">
            <CardContent className="p-16 text-center">
              <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                {searchQuery ? 'No articles found' : 'No articles yet'}
              </h3>
              <p className="text-gray-600 mb-8 text-lg leading-relaxed max-w-md mx-auto">
                {searchQuery
                  ? `We couldn't find any articles matching "${searchQuery}". Try different keywords or browse all articles.`
                  : 'Be the first to share your knowledge with the community.'
                }
              </p>
              {searchQuery ? (
                <Button onClick={clearSearch} className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 rounded-xl font-semibold">
                  Browse All Articles
                </Button>
              ) : (
                <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-xl font-semibold">
                  Write First Article
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        {/* Posts Grid */}
        {!loading && posts.length > 0 && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {posts.map((post) => (
                <PostCard
                  key={post.id}
                  post={post}
                  author={post.author}
                  onPostDeleted={handlePostDeleted}
                />
              ))}
            </div>

            {/* Load More Button */}
            {hasMore && (
              <div className="text-center">
                <Button
                  onClick={loadMore}
                  disabled={loadingMore}
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
                >
                  {loadingMore ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Loading...
                    </>
                  ) : (
                    'Load More Articles'
                  )}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
}
