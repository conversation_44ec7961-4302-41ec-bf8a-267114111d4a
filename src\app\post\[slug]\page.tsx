'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import Link from 'next/link';
import Layout from '@/components/layout/layout';
import { Button } from '@/components/ui/button';
import { getImageSrc } from '@/lib/utils/image-utils';
import PostContentRenderer from '@/components/ui/post-content-renderer';

import UniversalAdRenderer from '@/components/ads/universal-ad-renderer';
import '@/styles/post-view.css';

interface PostData {
  post: {
    ID: number;
    post_title: string;
    post_name: string;
    post_excerpt: string;
    post_content: string;
    post_status: string;
    post_date: string;
    post_modified: string;
    post_author: number;
    featured_image?: string;
    comment_count: number;
    beforeContentAds?: string;
    afterContentAds?: string;
  };
  author: {
    ID: number;
    user_login: string;
    display_name: string;
    user_email: string;
  } | null;
}

export default function PostPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const slug = params.slug as string;
  const [postData, setPostData] = useState<PostData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleting, setDeleting] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  const [relatedPosts, setRelatedPosts] = useState<any[]>([]);
  const [relatedPostsLoading, setRelatedPostsLoading] = useState(false);
  const [relatedPostsPage, setRelatedPostsPage] = useState(1);
  const [hasMoreRelatedPosts, setHasMoreRelatedPosts] = useState(false);
  const [loadingMorePosts, setLoadingMorePosts] = useState(false);

  useEffect(() => {
    if (slug) {
      fetchPost();
    }
  }, [slug]);

  useEffect(() => {
    if (postData?.post?.ID) {
      setRelatedPostsPage(1);
      fetchRelatedPosts(postData.post.ID, 1, false);
    }
  }, [postData?.post?.ID]);

  // Reading progress tracker
  useEffect(() => {
    const updateReadingProgress = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (scrollTop / docHeight) * 100;
      setReadingProgress(Math.min(100, Math.max(0, progress)));
    };

    window.addEventListener('scroll', updateReadingProgress);
    return () => window.removeEventListener('scroll', updateReadingProgress);
  }, []);



  const fetchPost = async () => {
    try {
      // First try to get by slug from our database utility
      const response = await fetch(`/api/posts/by-slug/${slug}`);
      const result = await response.json();

      if (result.success) {
        setPostData(result.data);
        // Fetch related posts after getting the main post
        fetchRelatedPosts(result.data.post.ID);
      } else {
        setError('Post not found');
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      setError('Failed to load post');
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedPosts = async (currentPostId: number, page: number = 1, append: boolean = false) => {
    try {
      if (page === 1) {
        setRelatedPostsLoading(true);
      } else {
        setLoadingMorePosts(true);
      }
      console.log('Fetching related posts for post ID:', currentPostId, 'Page:', page);

      const response = await fetch(`/api/posts/related/${currentPostId}?page=${page}&limit=6`);
      const result = await response.json();

      console.log('Related posts API response:', result);

      if (result.success) {
        if (append) {
          setRelatedPosts(prev => [...prev, ...(result.data || [])]);
        } else {
          setRelatedPosts(result.data || []);
        }
        setHasMoreRelatedPosts(result.pagination?.hasMore || false);
        console.log('Related posts set:', result.data?.length || 0, 'posts');
      } else {
        console.error('Failed to fetch related posts:', result.error);
      }
    } catch (error) {
      console.error('Error fetching related posts:', error);
    } finally {
      setRelatedPostsLoading(false);
      setLoadingMorePosts(false);
    }
  };

  const loadMoreRelatedPosts = async () => {
    if (post && !loadingMorePosts && hasMoreRelatedPosts) {
      const nextPage = relatedPostsPage + 1;
      setRelatedPostsPage(nextPage);
      await fetchRelatedPosts(post.ID, nextPage, true);
    }
  };

  const handleDeletePost = async () => {
    if (!postData?.post?.ID) return;

    const confirmDelete = window.confirm('Are you sure you want to delete this post?\n\nThis action cannot be undone.');

    if (!confirmDelete) return;

    setDeleting(true);
    try {
      const response = await fetch(`/api/posts/${postData.post.ID}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        alert('Post deleted successfully.');
        // No redirect - stay on the same page
      } else {
        alert(result.error || 'Failed to delete post.');
      }
    } catch (error) {
      console.error('Error deleting post:', error);
      alert('An error occurred while deleting the post.');
    } finally {
      setDeleting(false);
    }
  };

  // Check if current user can edit/delete this post
  const canEditPost = () => {
    if (!session?.user || !postData?.post) return false;

    const isOwner = postData.post.post_author === parseInt(session.user.id);
    const isAdminOrEditor = ['ADMIN', 'EDITOR'].includes(session.user.role);

    return isOwner || isAdminOrEditor;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded-lg mb-6"></div>
            <div className="flex items-center space-x-4 mb-8">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-32"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
            <div className="h-80 bg-gray-200 rounded-xl mb-8"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !postData) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Post Not Found</h1>
          <p className="text-gray-600 mb-8 text-lg">{error || 'The post you are looking for does not exist.'}</p>
          <Link href="/blog">
            <Button size="lg" className="px-8 py-3">Back to Blog</Button>
          </Link>
        </div>
      </Layout>
    );
  }

  const { post, author } = postData;

  return (
    <Layout>
      {/* Reading Progress Bar */}
      <div className="reading-progress">
        <div
          className="reading-progress-bar"
          style={{ width: `${readingProgress}%` }}
        ></div>
      </div>

      {/* Enhanced Hero Section */}
      <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>

        <div className="relative max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <div className="flex items-center space-x-2 text-sm text-blue-200">
              <Link href="/" className="hover:text-white transition-colors">Home</Link>
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <Link href="/blog" className="hover:text-white transition-colors">Blog</Link>
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-blue-100">Current Post</span>
            </div>
          </nav>

          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 leading-tight bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              {post.post_title}
            </h1>
          </div>

          {/* Enhanced Author and Meta Info */}
          <div className="flex flex-col lg:flex-row items-center justify-center space-y-4 lg:space-y-0 lg:space-x-8 px-4">
            {/* Author Info */}
            {author && (
              <Link
                href={`/author/${author.user_login}`}
                className="w-full sm:w-auto text-center bg-white/5 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/10 hover:bg-white/10 transition-all duration-300 flex items-center justify-center space-x-2 text-blue-100 min-w-[200px]"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm shadow">
                  {(author.display_name || author.user_login).charAt(0).toUpperCase()}
                </div>
                <div className="text-left">
                  <div className="font-semibold text-white">
                    {author.display_name || author.user_login}
                  </div>
                  <div className="text-xs text-blue-200">Author</div>
                </div>
              </Link>
            )}

            {/* Meta Information */}
            <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
              {/* Date */}
              <div className="w-full sm:w-auto text-center bg-white/5 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/10 min-w-[200px]">
                <div className="flex items-center justify-center space-x-2 text-blue-100">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <div>
                    <div className="font-semibold text-white">
                      {formatDate(post.post_date)}
                    </div>
                    <div className="text-xs text-blue-200">Published</div>
                  </div>
                </div>
              </div>

              {/* Reading Time */}
              <div className="w-full sm:w-auto text-center bg-white/5 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/10 min-w-[200px]">
                <div className="flex items-center justify-center space-x-2 text-blue-100">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <div className="font-semibold text-white">
                      {Math.ceil(post.post_content.split(' ').length / 200)} min
                    </div>
                    <div className="text-xs text-blue-200">Read time</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Edit and Delete Buttons for Authorized Users */}
          {canEditPost() && (
            <div className="flex justify-center mt-12 space-x-4">
              <Link href={`/dashboard/edit-post/${post.ID}`}>
                <Button className="bg-white/10 hover:bg-white/20 text-white px-8 py-3 flex items-center space-x-2 rounded-xl border border-white/20 backdrop-blur-sm transition-all duration-300">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <span>Edit Post</span>
                </Button>
              </Link>

              <Button
                onClick={handleDeletePost}
                disabled={deleting}
                className="bg-red-500/20 hover:bg-red-500/30 text-red-100 px-8 py-3 flex items-center space-x-2 rounded-xl border border-red-400/30 backdrop-blur-sm transition-all duration-300"
              >
                {deleting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-100"></div>
                    <span>Deleting...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    <span>Delete Post</span>
                  </>
                )}
              </Button>
            </div>
          )}
        </div>


      </div>

      <article className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">

        {/* Featured Image */}
        {post.featured_image && (
          <div className="mb-8 sm:mb-16 -mt-6 sm:-mt-12 max-w-4xl mx-auto px-4 sm:px-0">
            <div className="relative h-[250px] sm:h-[400px] lg:h-[500px] w-full rounded-2xl sm:rounded-3xl overflow-hidden shadow-xl sm:shadow-2xl group">
              <img
                src={post.featured_image}
                alt={post.post_title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>

              {/* Image Caption */}
              <div className="absolute bottom-6 left-6 right-6">
                <div className="bg-white/10 backdrop-blur-md rounded-xl px-4 py-2 border border-white/20">
                  <p className="text-white text-sm font-medium">{post.post_title}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Post Content */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden">
            {/* Content Header */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full"></div>
                  <h2 className="text-xl font-bold text-gray-900">Post Content</h2>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <span>Read and Enjoy</span>
                </div>
              </div>
            </div>

            {/* Content Body */}
            <div className="p-8 lg:p-12">
              {/* Before Content Ads */}
              {post.beforeContentAds && post.beforeContentAds.trim() && (
                <div className="mb-8 ad-container before-content-ads" style={{
                  background: '#f9fafb',
                  border: '1px solid rgb(0 0 0)',
                  borderRadius: '8px',
                  padding: '1px',
                  textAlign: 'center',
                  margin: '16px 0px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'relative'
                }}>
                  <div className="sponsored-ads-label">
                    Sponsored Ads
                  </div>
                  <UniversalAdRenderer adCode={post.beforeContentAds} />
                </div>
              )}

              <div className="prose prose-lg prose-gray max-w-none post-content">
                <PostContentRenderer
                  content={post.post_content}
                  isEditable={canEditPost()}
                  postId={post.ID}
                  onContentUpdate={(newContent) => {
                    // Update the post content in state
                    setPostData(prev => prev ? {
                      ...prev,
                      post: { ...prev.post, post_content: newContent }
                    } : null);
                  }}
                  className="post-content"
                />
              </div>

              {/* After Content Ads */}
              {post.afterContentAds && post.afterContentAds.trim() && (
                <div className="mt-8 ad-container after-content-ads" style={{
                  background: '#f9fafb',
                  border: '1px solid rgb(0 0 0)',
                  borderRadius: '8px',
                  padding: '1px',
                  textAlign: 'center',
                  margin: '16px 0px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'relative'
                }}>
                  <div className="sponsored-ads-label">
                    Sponsored Ads
                  </div>
                  <UniversalAdRenderer adCode={post.afterContentAds} />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Enhanced Post Footer */}
        <div className="mt-16 space-y-8 max-w-4xl mx-auto">
          {/* Social Share Section */}
          <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl p-8 border border-blue-100">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Did you like this post?</h3>
              <p className="text-gray-600">Share it with your friends and give them a chance to learn too</p>
            </div>

            <div className="flex flex-wrap justify-center gap-4">
              {/* Facebook Share */}
              <Button
                variant="outline"
                className="bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700 px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105"
                onClick={() => {
                  const url = encodeURIComponent(window.location.href);
                  const title = encodeURIComponent(post.post_title);
                  window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${title}`, '_blank', 'width=600,height=400');
                }}
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                Facebook
              </Button>

              {/* Twitter Share */}
              <Button
                variant="outline"
                className="bg-sky-500 hover:bg-sky-600 text-white border-sky-500 hover:border-sky-600 px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105"
                onClick={() => {
                  const url = encodeURIComponent(window.location.href);
                  const title = encodeURIComponent(post.post_title);
                  window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank', 'width=600,height=400');
                }}
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
                Twitter
              </Button>

              {/* LinkedIn Share */}
              <Button
                variant="outline"
                className="bg-blue-700 hover:bg-blue-800 text-white border-blue-700 hover:border-blue-800 px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105"
                onClick={() => {
                  const url = encodeURIComponent(window.location.href);
                  const title = encodeURIComponent(post.post_title);
                  window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`, '_blank', 'width=600,height=400');
                }}
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
                LinkedIn
              </Button>

              {/* Copy Link */}
              <Button
                variant="outline"
                className="bg-gray-600 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-700 px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105"
                onClick={() => {
                  navigator.clipboard.writeText(window.location.href);
                  // Add toast notification here if needed
                }}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Copy Link
              </Button>

              {/* Print */}
              <Button
                variant="outline"
                className="bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700 px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105"
                onClick={() => window.print()}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                </svg>
                Print
              </Button>
            </div>
          </div>

          {/* Enhanced Related Posts Section */}
          <div className="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 rounded-3xl p-8 border border-indigo-100 shadow-lg">
            <div className="text-center mb-10">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full mb-6 shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                </svg>
              </div>
              <h3 className="text-3xl font-bold text-gray-900 mb-3">Related Articles</h3>
              <p className="text-gray-600 text-lg">Discover more content you might find interesting</p>
            </div>

            {relatedPostsLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="bg-white rounded-2xl shadow-lg overflow-hidden animate-pulse">
                    <div className="h-52 bg-gray-200"></div>
                    <div className="p-6 space-y-4">
                      <div className="h-6 bg-gray-200 rounded"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {!relatedPostsLoading && relatedPosts.length > 0 && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {relatedPosts.map((relatedPost) => (
                    <Link
                      key={relatedPost.ID}
                      href={`/post/${relatedPost.post_name}`}
                      className="group block"
                    >
                      <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 group-hover:border-indigo-200 transform hover:-translate-y-2 hover:scale-105 h-80 flex flex-col">
                        {relatedPost.featured_image ? (
                          <div className="relative h-48 overflow-hidden">
                            <img
                              src={relatedPost.featured_image}
                              alt={relatedPost.post_title}
                              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                          </div>
                        ) : (
                          <div className="h-48 bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center">
                            <svg className="w-12 h-12 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                            </svg>
                          </div>
                        )}

                        <div className="p-6 flex-1 flex flex-col justify-center">
                          <h4 className="font-bold text-lg text-gray-900 line-clamp-3 group-hover:text-indigo-600 transition-colors leading-tight text-center">
                            {relatedPost.post_title}
                          </h4>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>

                {/* Load More Button */}
                {hasMoreRelatedPosts && (
                  <div className="text-center mt-10">
                    <Button
                      onClick={loadMoreRelatedPosts}
                      disabled={loadingMorePosts}
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                      {loadingMorePosts ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Loading...
                        </>
                      ) : (
                        <>
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                          Load More Articles
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </>
            )}

            {!relatedPostsLoading && relatedPosts.length === 0 && (
              <div className="text-center py-12">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-6">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-2">No Related Articles Found</h4>
                <p className="text-gray-600 mb-6">We couldn't find any related articles at the moment.</p>
                {/* Debug info */}
                <div className="text-xs text-gray-500 mb-4">
                  Debug: Loading={relatedPostsLoading.toString()}, Posts count={relatedPosts.length}
                </div>
                <Link href="/blog">
                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300">
                    Explore All Articles
                  </Button>
                </Link>
              </div>
            )}
          </div>

        </div>


      </article>
    </Layout>
  );
}
