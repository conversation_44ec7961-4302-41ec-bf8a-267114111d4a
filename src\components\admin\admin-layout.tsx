'use client';

import { useState } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { USER_STATUS } from '@/lib/db/schema';
import {
  LayoutDashboard,
  Users,
  FileText,
  FolderOpen,
  Tags,
  MessageSquare,
  Settings,
  BarChart3,
  Shield,
  Menu,
  X,
  LogOut,
  User,
  Bell,
  Search,
  ChevronRight,
  Home,
  Plus,
  Activity,
  HelpCircle,
  Moon,
  Sun,
  Maximize2,
  Minimize2,
  Clock,
  Globe
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { useTheme } from '@/components/providers/theme-provider';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { useAdminStats } from '@/hooks/use-admin-stats';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const navigationSections = [
  {
    title: 'Overview',
    items: [
      {
        name: 'Dashboard',
        href: '/admin',
        icon: LayoutDashboard,
        description: 'Overview and statistics',
        badge: null
      }
    ]
  },
  {
    title: 'Content Management',
    items: [
      {
        name: 'Posts',
        href: '/admin/posts',
        icon: FileText,
        description: 'Manage blog posts',
        badge: null
      },
      {
        name: 'Post Moderation',
        href: '/admin/posts/moderate',
        icon: Shield,
        description: 'Review pending posts',
        badge: 'pending'
      },
      {
        name: 'Categories',
        href: '/admin/categories',
        icon: FolderOpen,
        description: 'Organize content categories',
        badge: null
      },
      {
        name: 'Tags',
        href: '/admin/tags',
        icon: Tags,
        description: 'Manage content tags',
        badge: null
      },

    ]
  },
  {
    title: 'User Management',
    items: [
      {
        name: 'Users',
        href: '/admin/users',
        icon: Users,
        description: 'Manage user accounts',
        badge: null
      },
      {
        name: 'Pending Approvals',
        href: '/admin/users/pending',
        icon: Clock,
        description: 'Review new registrations',
        badge: 'new'
      },

    ]
  },
  {
    title: 'Settings',
    items: [
      {
        name: 'Settings',
        href: '/admin/settings',
        icon: Settings,
        description: 'Site configuration',
        badge: null
      }
    ]
  }
];



export default function AdminLayout({ children }: AdminLayoutProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  // const { theme, toggleTheme } = useTheme();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { stats } = useAdminStats();

  // Generate breadcrumbs from pathname
  const generateBreadcrumbs = () => {
    const paths = pathname.split('/').filter(Boolean);
    const breadcrumbs = [{ name: 'Admin', href: '/admin' }];

    let currentPath = '';
    paths.slice(1).forEach((path) => {
      currentPath += `/${path}`;
      const name = path.charAt(0).toUpperCase() + path.slice(1);
      breadcrumbs.push({ name, href: `/admin${currentPath}` });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Redirect if not authenticated or not admin/editor
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/login');
    return null;
  }

  // Check if user is pending
  if (session?.user && session.user.status === USER_STATUS.PENDING) {
    router.push('/pending');
    return null;
  }

  // Temporary: Allow all logged-in users to access admin (for testing)
  // TODO: Restore admin-only access later
  // if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role) || session.user.status !== USER_STATUS.APPROVED) {
  //   return (
  //     <div className="min-h-screen flex items-center justify-center bg-gray-50">
  //       <div className="text-center">
  //         <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
  //         <p className="text-gray-600 mb-6">You don't have permission to access the admin panel.</p>
  //         <Button onClick={() => router.push('/dashboard')}>
  //           Go to Dashboard
  //         </Button>
  //       </div>
  //     </div>
  //   );
  // }

  if (!session?.user || session.user.status !== USER_STATUS.APPROVED) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">Please ensure your account is approved.</p>
          <Button onClick={() => router.push('/dashboard')}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`admin-layout min-h-screen transition-colors duration-200 bg-[var(--content-bg)] ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75 backdrop-blur-sm"></div>
        </div>
      )}

      {/* Sidebar */}
      <div className={`
        admin-sidebar fixed inset-y-0 left-0 z-50 bg-[var(--sidebar-bg)] shadow-xl transform transition-all duration-300 ease-in-out border-r border-[var(--sidebar-border)]
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:-translate-x-0'}
        ${sidebarCollapsed ? 'lg:w-16' : 'lg:w-72'}
        w-72
      `}>
        <div className="flex flex-col h-full">
          {/* Logo and controls */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-[var(--sidebar-border)]">
            <Link href="/admin" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                <LayoutDashboard className="w-6 h-6 text-white" />
              </div>
              {!sidebarCollapsed && (
                <div>
                  <span className="text-xl font-bold text-[var(--sidebar-text)]">Wikify</span>
                  <div className="text-xs text-[var(--sidebar-text-muted)]">Admin Panel</div>
                </div>
              )}
            </Link>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                className="hidden lg:block p-1.5 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                {sidebarCollapsed ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
              </button>
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden p-1.5 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>





          {/* Navigation */}
          <nav className="flex-1 px-3 py-4 space-y-6 overflow-y-auto">
            {navigationSections.map((section) => (
              <div key={section.title}>
                {!sidebarCollapsed && (
                  <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3 px-3">
                    {section.title}
                  </div>
                )}
                <div className="space-y-1">
                  {section.items.map((item) => {
                    const isActive = pathname === item.href;
                    const Icon = item.icon;

                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`
                          group flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200
                          ${isActive
                            ? 'bg-[var(--sidebar-active)] text-[var(--sidebar-active-text)] shadow-sm border-l-4 border-[var(--primary)]'
                            : 'text-[var(--sidebar-text)] hover:bg-[var(--sidebar-hover)] hover:text-[var(--sidebar-text)]'
                          }
                          ${sidebarCollapsed ? 'justify-center' : ''}
                        `}
                        onClick={() => setSidebarOpen(false)}
                        title={sidebarCollapsed ? item.name : undefined}
                      >
                        <Icon className={`
                          h-5 w-5 flex-shrink-0 transition-colors
                          ${isActive ? 'text-[var(--sidebar-active-text)]' : 'text-[var(--sidebar-text-muted)] group-hover:text-[var(--sidebar-text)]'}
                          ${sidebarCollapsed ? '' : 'mr-3'}
                        `} />
                        {!sidebarCollapsed && (
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <span className="truncate">{item.name}</span>
                              {(item.badge &&
                                ((item.badge === 'pending' && stats?.overview?.pending && stats.overview.pending > 0) ||
                                 (item.badge === 'new' && stats?.recentActivity?.newPendingPosts && stats.recentActivity.newPendingPosts > 0) ||
                                 (item.badge !== 'pending' && item.badge !== 'new'))) && (
                                <span className="ml-2 px-2 py-0.5 text-xs bg-[var(--destructive)]/10 text-[var(--destructive)] rounded-full">
                                  {item.badge === 'pending' && stats?.overview?.pending
                                    ? stats.overview.pending
                                    : item.badge === 'new' && stats?.recentActivity?.newPendingPosts
                                    ? stats.recentActivity.newPendingPosts
                                    : item.badge}
                                </span>
                              )}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                              {item.description}
                            </div>
                          </div>
                        )}
                      </Link>
                    );
                  })}
                </div>
              </div>
            ))}
          </nav>


        </div>
      </div>

      {/* Main content */}
      <div className="admin-main-content">
        {/* Enhanced Top bar */}
        <div className="sticky top-0 z-10 bg-[var(--header-bg)]/80 backdrop-blur-md shadow-sm border-b border-[var(--header-border)]">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-lg text-[var(--sidebar-text-muted)] hover:text-[var(--sidebar-text)] hover:bg-[var(--sidebar-hover)] transition-colors"
              >
                <Menu className="w-5 h-5" />
              </button>

              {/* Breadcrumbs */}
              <nav className="hidden sm:flex items-center space-x-2 text-sm">
                {breadcrumbs.map((crumb, index) => (
                  <div key={crumb.href} className="flex items-center">
                    {index > 0 && <ChevronRight className="w-4 h-4 text-[var(--muted-foreground)] mx-2" />}
                    <Link
                      href={crumb.href}
                      className={`
                        px-2 py-1 rounded-md transition-colors
                        ${index === breadcrumbs.length - 1
                          ? 'text-[var(--header-text)] font-medium'
                          : 'text-[var(--muted-foreground)] hover:text-[var(--header-text)]'
                        }
                      `}
                    >
                      {crumb.name}
                    </Link>
                  </div>
                ))}
              </nav>
            </div>

            <div className="flex items-center space-x-3">
              {/* Search */}
              <div className="hidden md:block relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search admin..."
                  className="pl-10 pr-4 py-2 w-64 text-sm border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Notifications */}
              <button className="relative p-2 rounded-lg text-[var(--sidebar-text-muted)] hover:text-[var(--sidebar-text)] hover:bg-[var(--sidebar-hover)] transition-colors">
                <Bell className="w-5 h-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-[var(--destructive)] rounded-full"></span>
              </button>

              {/* Visit Site button */}
              <Link href="/" target="_blank" rel="noopener noreferrer">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <Globe className="w-4 h-4" />
                  <span className="hidden sm:inline">Visit Site</span>
                </Button>
              </Link>

              {/* Theme toggle - temporarily disabled for build */}
              {/* <ThemeToggle className="text-[var(--sidebar-text-muted)] hover:text-[var(--sidebar-text)] hover:bg-[var(--sidebar-hover)]" /> */}

              {/* User menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <span className="hidden sm:block text-sm font-medium text-gray-700 dark:text-gray-300">
                      {session.user.name || session.user.username}
                    </span>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard" className="flex items-center">
                      <User className="w-4 h-4 mr-2" />
                      Profile
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/admin/settings" className="flex items-center">
                      <Settings className="w-4 h-4 mr-2" />
                      Settings
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/help" className="flex items-center">
                      <HelpCircle className="w-4 h-4 mr-2" />
                      Help & Support
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => signOut()} className="text-red-600 dark:text-red-400">
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 min-h-screen">
          <div className="p-6 max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
