'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import RichTextEditor from './rich-text-editor';
import ImageUpload from './image-upload';

interface PostFormProps {
  initialData?: {
    id?: number;
    title: string;
    content: string;
    excerpt: string;
    status: string;
    featuredImage?: string;
    categories?: number[];
    beforeContentAds?: string;
    afterContentAds?: string;
  };
  onSubmit?: (data: any) => void;
  isEditing?: boolean;
}

const PostForm: React.FC<PostFormProps> = ({
  initialData,
  onSubmit,
  isEditing = false,
}) => {
  const { data: session } = useSession();
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    content: initialData?.content || '',
    excerpt: initialData?.excerpt || '',
    status: initialData?.status || 'draft',
    featuredImage: initialData?.featuredImage || '',
    categories: initialData?.categories || [],
    beforeContentAds: initialData?.beforeContentAds || '',
    afterContentAds: initialData?.afterContentAds || '',
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [categories, setCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [autoSaving, setAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showImagePreview, setShowImagePreview] = useState(false);

  // Load categories on component mount
  useEffect(() => {
    loadCategories();
  }, []);

  // Update form data when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title || '',
        content: initialData.content || '',
        excerpt: initialData.excerpt || '',
        status: initialData.status || 'draft',
        featuredImage: initialData.featuredImage || '',
        categories: initialData.categories || [],
        beforeContentAds: initialData.beforeContentAds || '',
        afterContentAds: initialData.afterContentAds || '',
      });
    }
  }, [initialData]);

  const loadCategories = async () => {
    try {
      setCategoriesLoading(true);
      // Include all categories for post forms, even empty ones
      const response = await fetch('/api/categories?for_posts=true&include_empty=true');
      const result = await response.json();

      if (result.success) {
        setCategories(result.data);
      } else {
        console.error('Failed to load categories:', result.error);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Category selection handlers
  const handleCategoryToggle = (categoryId: number) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.includes(categoryId)
        ? prev.categories.filter(id => id !== categoryId)
        : [...prev.categories, categoryId]
    }));
  };

  // Word count calculation
  const getWordCount = (text: string) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  // Auto-save functionality (temporarily disabled to prevent issues)
  // useEffect(() => {
  //   if (!formData.title && !formData.content) return;
  //   if (isEditing) return; // Only auto-save for new posts

  //   const autoSaveTimer = setTimeout(async () => {
  //     if (formData.title.trim() && formData.content.trim() && formData.title.length > 5) {
  //       setAutoSaving(true);
  //       try {
  //         // Auto-save as draft only if we have substantial content
  //         const response = await fetch('/api/posts', {
  //           method: 'POST',
  //           headers: {
  //             'Content-Type': 'application/json',
  //           },
  //           body: JSON.stringify({
  //             ...formData,
  //             status: 'draft',
  //           }),
  //         });

  //         if (response.ok) {
  //           setLastSaved(new Date());
  //         }
  //       } catch (error) {
  //         // Silent fail for auto-save
  //         console.log('Auto-save failed:', error);
  //       } finally {
  //         setAutoSaving(false);
  //       }
  //     }
  //   }, 60000); // Auto-save every 60 seconds

  //   return () => clearTimeout(autoSaveTimer);
  // }, [formData.title, formData.content, isEditing]); // Only watch title and content for auto-save

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            handleSubmit('draft');
            break;
          case 'Enter':
            e.preventDefault();
            handleSubmit('publish');
            break;
          case 'p':
            if (formData.featuredImage) {
              e.preventDefault();
              setShowImagePreview(true);
            }
            break;
        }
      }

      // ESC to close image preview
      if (e.key === 'Escape' && showImagePreview) {
        setShowImagePreview(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [formData, showImagePreview]);

  const handleSubmit = async (status: string) => {
    // Validation
    if (!formData.title.trim()) {
      setError('Post title is required');
      setSuccess('');
      return;
    }

    if (!formData.content.trim()) {
      setError('Post content is required');
      setSuccess('');
      return;
    }

    if (!formData.featuredImage.trim()) {
      setError('Featured image is required');
      setSuccess('');
      return;
    }

    if (!formData.categories || formData.categories.length === 0) {
      setError('At least one category must be selected');
      setSuccess('');
      return;
    }

    if (formData.title.length > 100) {
      setError('Title should be under 100 characters');
      setSuccess('');
      return;
    }

    if (formData.excerpt.length > 300) {
      setError('Excerpt should be under 300 characters');
      setSuccess('');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const submitData = {
        ...formData,
        status,
      };

      // Only include ad fields if user has permission
      if (!session?.user?.canInsertAds) {
        delete submitData.beforeContentAds;
        delete submitData.afterContentAds;
      }

      if (onSubmit) {
        await onSubmit(submitData);
      } else {
        // Default API call
        const url = isEditing ? `/api/posts/${initialData?.id}` : '/api/posts';
        const method = isEditing ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(submitData),
        });

        const result = await response.json();

        if (result.success) {
          setSuccess(
            status === 'publish'
              ? 'Post submitted for review successfully!'
              : 'Post saved as draft successfully!'
          );
          setLastSaved(new Date());

          // Redirect after a short delay to show success message
          setTimeout(() => {
            router.push('/dashboard');
          }, 1500);
        } else {
          setError(result.error || result.details || 'Failed to save post');
        }
      }
    } catch (error) {
      console.error('Submit error:', error);
      setError(error instanceof Error ? error.message : 'An error occurred while saving the post');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (imageData: {
    url: string;
    public_id: string;
    width?: number;
    height?: number;
    format?: string;
    size?: number;
    compression_ratio?: string;
    optimized?: boolean;
    preset?: string;
  }) => {
    setFormData(prev => ({
      ...prev,
      featuredImage: imageData.url,
    }));

    // Show optimization info if available
    if (imageData.optimized && imageData.compression_ratio) {
      console.log(`Image optimized: ${imageData.format?.toUpperCase()} format, ${imageData.compression_ratio}% compression`);
    }
  };

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center py-8 bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Authentication Required</h2>
          <p className="text-gray-600 mb-4">Please sign in to create or edit posts.</p>
          <button
            onClick={() => window.location.href = '/auth/signin'}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {isEditing ? 'Edit Post' : 'Create New Post'}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
            {isEditing
              ? 'Update your post with new content and publish your changes.'
              : 'Share your thoughts with the world. Create engaging content that resonates with your audience.'
            }
          </p>


        </div>

        {/* Main Form */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-2 space-y-6">
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Post Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {error && (
                  <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    </div>
                  </div>
                )}

                {success && (
                  <div className="bg-green-50 border-l-4 border-green-400 p-4 rounded-r-lg">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-green-700">{success}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Title */}
                <div className="space-y-2">
                  <Input
                    label="Post Title *"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter an engaging title for your post..."
                    required
                    className="text-lg font-medium"
                    helperText={`${formData.title.length}/100 characters`}
                    error={formData.title.length > 100 ? 'Title should be under 100 characters' : ''}
                  />
                </div>

                {/* Excerpt */}
                <div className="space-y-2">
                  <Textarea
                    label="Post Excerpt"
                    value={formData.excerpt}
                    onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                    placeholder="Write a compelling summary that will appear in post previews..."
                    rows={3}
                    helperText={`${formData.excerpt.length}/300 characters - A brief description that helps readers understand what your post is about`}
                    error={formData.excerpt.length > 300 ? 'Excerpt should be under 300 characters' : ''}
                  />
                </div>

                {/* Content Editor */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Post Content *
                  </label>
                  <div className="border border-gray-200 rounded-lg overflow-hidden bg-white">
                    <RichTextEditor
                      content={formData.content}
                      onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                      placeholder=""
                    />
                  </div>
                </div>

                {/* Ad Management - Only show if user has ads permission */}
                {session?.user?.canInsertAds && (
                  <div className="space-y-4 pt-6 border-t border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Ad Management
                    </h3>

                    {/* Before Content Ads */}
                    <div className="space-y-2">
                      <label htmlFor="beforeContentAds" className="block text-sm font-medium text-gray-700">
                        Before Post Content Ads
                      </label>
                      <Textarea
                        id="beforeContentAds"
                        value={formData.beforeContentAds}
                        onChange={(e) => setFormData(prev => ({ ...prev, beforeContentAds: e.target.value }))}
                        placeholder="Paste your ad code here (HTML, JavaScript, etc.)"
                        className="min-h-[120px] font-mono text-sm resize-none"
                        style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, source-code-pro, monospace' }}
                      />
                      <p className="text-xs text-gray-500">
                        Ad code that will appear before the main post content
                      </p>
                    </div>

                    {/* After Content Ads */}
                    <div className="space-y-2">
                      <label htmlFor="afterContentAds" className="block text-sm font-medium text-gray-700">
                        After Post Content Ads
                      </label>
                      <Textarea
                        id="afterContentAds"
                        value={formData.afterContentAds}
                        onChange={(e) => setFormData(prev => ({ ...prev, afterContentAds: e.target.value }))}
                        placeholder="Paste your ad code here (HTML, JavaScript, etc.)"
                        className="min-h-[120px] font-mono text-sm resize-none"
                        style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, source-code-pro, monospace' }}
                      />
                      <p className="text-xs text-gray-500">
                        Ad code that will appear after the main post content
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Featured Image */}
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Featured Image *
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ImageUpload
                  currentImage={formData.featuredImage}
                  onUpload={handleImageUpload}
                  onError={setError}
                  folder="posts"
                  preset="featured-image"
                  optimize={true}
                  onPreview={() => setShowImagePreview(true)}
                />
                <p className="text-xs text-gray-500 mt-2">
                  Upload an eye-catching image that represents your post content (Required)
                </p>
              </CardContent>
            </Card>

            {/* Categories */}
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  Categories *
                </CardTitle>
              </CardHeader>
              <CardContent>
                {categoriesLoading ? (
                  <div className="space-y-2">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {categories.map((category) => (
                      <label
                        key={category.id}
                        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      >
                        <input
                          type="checkbox"
                          checked={formData.categories.includes(category.id)}
                          onChange={() => handleCategoryToggle(category.id)}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <span className="text-sm text-gray-700 flex-1">
                          {category.name}
                        </span>
                      </label>
                    ))}
                  </div>
                )}
                <p className="text-xs text-gray-500 mt-3">
                  Select one or more categories that best describe your post content (Required)
                </p>
              </CardContent>
            </Card>



            {/* Post Status & Actions */}
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Publish Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Status Display */}
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Status:</span>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    formData.status === 'publish'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {formData.status === 'publish' ? 'Published' : 'Draft'}
                  </span>
                </div>

                {/* Word Count */}
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Word Count:</span>
                  <span className="text-sm font-semibold text-blue-700">
                    {getWordCount(formData.content)} words
                  </span>
                </div>

                {/* Save Status */}
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Save Status:</span>
                  <span className="text-xs text-gray-600 flex items-center">
                    {loading ? (
                      <>
                        <svg className="animate-spin h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Saving...
                      </>
                    ) : success ? (
                      <>
                        <svg className="h-3 w-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Saved successfully
                      </>
                    ) : (
                      'Ready to save'
                    )}
                  </span>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button
                    variant="outline"
                    onClick={() => handleSubmit('draft')}
                    disabled={loading || !formData.title.trim() || !formData.content.trim() || !formData.featuredImage.trim() || !formData.categories || formData.categories.length === 0}
                    loading={loading}
                    className="w-full justify-center"
                  >
                    {!loading && (
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    )}
                    Save as Draft
                  </Button>

                  <Button
                    variant="primary"
                    onClick={() => handleSubmit('publish')}
                    disabled={loading || !formData.title.trim() || !formData.content.trim() || !formData.featuredImage.trim() || !formData.categories || formData.categories.length === 0}
                    loading={loading}
                    className="w-full justify-center bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                  >
                    {!loading && (
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                    )}
                    {loading ? 'Saving...' : (isEditing ? 'Update Post' : 'Submit for Review')}
                  </Button>

                  <Button
                    variant="ghost"
                    onClick={() => router.back()}
                    disabled={loading}
                    className="w-full justify-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>


          </div>
        </div>
      </div>

      {/* Image Preview Modal */}
      {showImagePreview && formData.featuredImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4 backdrop-blur-sm"
          onClick={() => setShowImagePreview(false)}
        >
          <div className="relative max-w-6xl max-h-full" onClick={(e) => e.stopPropagation()}>
            {/* Header */}
            <div className="absolute -top-16 left-0 right-0 flex items-center justify-between text-white">
              <div>
                <h3 className="text-lg font-semibold">Featured Image Preview</h3>
                <p className="text-sm text-gray-300">Click outside or press ESC to close</p>
              </div>
              <button
                onClick={() => setShowImagePreview(false)}
                className="p-2 hover:bg-white/10 rounded-full transition-colors"
                title="Close preview (ESC)"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Image */}
            <div className="relative bg-white rounded-lg overflow-hidden shadow-2xl">
              <Image
                src={formData.featuredImage}
                alt="Featured image preview"
                width={1200}
                height={800}
                className="max-w-full max-h-[80vh] object-contain"
                priority
              />
            </div>

            {/* Footer */}
            <div className="absolute -bottom-12 left-0 right-0 text-center">
              <a
                href={formData.featuredImage}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-white hover:text-blue-300 transition-colors text-sm"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Open in new tab
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PostForm;
