import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface AdminStats {
  overview: {
    pending: number;
    approved: number;
    rejected: number;
    draft: number;
    total: number;
  };
  recentActivity: {
    newPendingPosts: number;
  };
}

export function useAdminStats() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    if (!session?.user || !['ADMIN', 'EDITOR'].includes(session.user.role)) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/posts/stats');
      const result = await response.json();

      if (result.success) {
        setStats(result.data);
      } else {
        setError(result.error || 'Failed to fetch stats');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    
    // Refresh stats every 30 seconds
    const interval = setInterval(fetchStats, 30000);
    
    return () => clearInterval(interval);
  }, [session]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  };
}
