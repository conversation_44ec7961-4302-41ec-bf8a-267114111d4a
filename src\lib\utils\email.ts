import nodemailer from 'nodemailer';

interface EmailConfig {
  smtpHost: string;
  smtpPort: string;
  smtpUser: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;
}

interface SendEmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

// Get email configuration from environment or settings
export async function getEmailConfig(): Promise<EmailConfig | null> {
  // First try to get from environment variables
  if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASSWORD) {
    return {
      smtpHost: process.env.SMTP_HOST,
      smtpPort: process.env.SMTP_PORT || '587',
      smtpUser: process.env.SMTP_USER,
      smtpPassword: process.env.SMTP_PASSWORD,
      fromEmail: process.env.FROM_EMAIL || process.env.SMTP_USER,
      fromName: process.env.FROM_NAME || 'Wikify Blog',
    };
  }

  // TODO: If environment variables are not set, get from database settings
  // This would require fetching from the settings table
  // For now, return null if no environment config
  return null;
}

// Create nodemailer transporter
export async function createEmailTransporter() {
  const config = await getEmailConfig();
  
  if (!config) {
    throw new Error('Email configuration not found. Please set SMTP environment variables.');
  }

  const transporter = nodemailer.createTransport({
    host: config.smtpHost,
    port: parseInt(config.smtpPort),
    secure: parseInt(config.smtpPort) === 465, // true for 465, false for other ports
    auth: {
      user: config.smtpUser,
      pass: config.smtpPassword,
    },
  });

  // Verify connection
  try {
    await transporter.verify();
    return transporter;
  } catch (error) {
    console.error('Email transporter verification failed:', error);
    throw new Error('Failed to connect to email server. Please check your SMTP configuration.');
  }
}

// Send email function
export async function sendEmail(options: SendEmailOptions): Promise<boolean> {
  try {
    const config = await getEmailConfig();
    if (!config) {
      console.error('Email configuration not found');

      // In development, log the email instead of sending
      if (process.env.NODE_ENV === 'development') {
        console.log('=== DEVELOPMENT EMAIL ===');
        console.log('To:', options.to);
        console.log('Subject:', options.subject);
        console.log('HTML Content:', options.html);
        console.log('========================');
        return true; // Return success for development
      }

      return false;
    }

    const transporter = await createEmailTransporter();

    const mailOptions = {
      from: `"${config.fromName}" <${config.fromEmail}>`,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text || options.html.replace(/<[^>]*>/g, ''), // Strip HTML for text version
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return true;
  } catch (error) {
    console.error('Failed to send email:', error);

    // In development, log the email instead of failing
    if (process.env.NODE_ENV === 'development') {
      console.log('=== DEVELOPMENT EMAIL (FALLBACK) ===');
      console.log('To:', options.to);
      console.log('Subject:', options.subject);
      console.log('HTML Content:', options.html);
      console.log('===================================');
      return true; // Return success for development
    }

    return false;
  }
}

// Generate password reset email HTML
export function generatePasswordResetEmail(resetUrl: string, username: string): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Password Reset - Wikify Blog</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 8px 8px 0 0;
        }
        .content {
          background-color: #ffffff;
          padding: 30px;
          border: 1px solid #e9ecef;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 0 0 8px 8px;
          font-size: 14px;
          color: #6c757d;
        }
        .button {
          display: inline-block;
          background-color: #007bff;
          color: white;
          padding: 12px 24px;
          text-decoration: none;
          border-radius: 4px;
          margin: 20px 0;
        }
        .warning {
          background-color: #fff3cd;
          border: 1px solid #ffeaa7;
          padding: 15px;
          border-radius: 4px;
          margin: 20px 0;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Wikify Blog</h1>
        <h2>Password Reset Request</h2>
      </div>
      
      <div class="content">
        <p>Hello ${username},</p>
        
        <p>We received a request to reset your password for your Wikify Blog account. If you made this request, click the button below to reset your password:</p>
        
        <div style="text-align: center;">
          <a href="${resetUrl}" class="button">Reset Password</a>
        </div>
        
        <p>Or copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">
          ${resetUrl}
        </p>
        
        <div class="warning">
          <strong>Important:</strong>
          <ul>
            <li>This link will expire in 1 hour for security reasons</li>
            <li>If you didn't request this password reset, please ignore this email</li>
            <li>Your password will remain unchanged until you create a new one</li>
          </ul>
        </div>
        
        <p>If you're having trouble with the button above, copy and paste the URL into your web browser.</p>
        
        <p>Best regards,<br>The Wikify Blog Team</p>
      </div>
      
      <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>If you need help, please contact our support team.</p>
      </div>
    </body>
    </html>
  `;
}

// Generate account registration welcome email HTML
export function generateWelcomeEmail(username: string, displayName: string): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Wikify Blog</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 8px 8px 0 0;
        }
        .content {
          background-color: #ffffff;
          padding: 30px;
          border: 1px solid #e9ecef;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 0 0 8px 8px;
          font-size: 14px;
          color: #6c757d;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #007bff;
        }
        .status-badge {
          display: inline-block;
          padding: 8px 16px;
          background-color: #ffc107;
          color: #212529;
          border-radius: 20px;
          font-weight: bold;
          margin: 10px 0;
        }
        .info-box {
          background-color: #e7f3ff;
          border-left: 4px solid #007bff;
          padding: 15px;
          margin: 20px 0;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Wikify Blog</div>
        <h1>Welcome to Our Community!</h1>
      </div>

      <div class="content">
        <p>Hello ${displayName || username},</p>

        <p>Thank you for registering with Wikify Blog! We're excited to have you join our community of writers and readers.</p>

        <div class="info-box">
          <h3>Account Status</h3>
          <div class="status-badge">Pending Approval</div>
          <p>Your account is currently under review by our administrators. You will receive another email once your account has been approved and you can start using all features.</p>
        </div>

        <h3>What happens next?</h3>
        <ul>
          <li>Our team will review your registration within 24-48 hours</li>
          <li>You'll receive an email notification once your account is approved</li>
          <li>After approval, you can sign in and start creating content</li>
        </ul>

        <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

        <p>Best regards,<br>The Wikify Blog Team</p>
      </div>

      <div class="footer">
        <p>This email was sent to you because you registered for an account on Wikify Blog.</p>
        <p>If you didn't create this account, please ignore this email.</p>
      </div>
    </body>
    </html>
  `;
}

// Generate account approval email HTML
export function generateApprovalEmail(username: string, displayName: string, canInsertAds: boolean): string {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const loginUrl = `${baseUrl}/auth/signin`;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Account Approved - Wikify Blog</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #d4edda;
          padding: 20px;
          text-align: center;
          border-radius: 8px 8px 0 0;
        }
        .content {
          background-color: #ffffff;
          padding: 30px;
          border: 1px solid #e9ecef;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 0 0 8px 8px;
          font-size: 14px;
          color: #6c757d;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #28a745;
        }
        .status-badge {
          display: inline-block;
          padding: 8px 16px;
          background-color: #28a745;
          color: white;
          border-radius: 20px;
          font-weight: bold;
          margin: 10px 0;
        }
        .button {
          display: inline-block;
          padding: 12px 24px;
          background-color: #007bff;
          color: white;
          text-decoration: none;
          border-radius: 5px;
          font-weight: bold;
          margin: 20px 0;
        }
        .permissions-box {
          background-color: #f8f9fa;
          border: 1px solid #dee2e6;
          padding: 15px;
          margin: 20px 0;
          border-radius: 5px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Wikify Blog</div>
        <h1>🎉 Account Approved!</h1>
      </div>

      <div class="content">
        <p>Hello ${displayName || username},</p>

        <p>Great news! Your Wikify Blog account has been approved and is now active.</p>

        <div class="status-badge">Account Approved</div>

        <div class="permissions-box">
          <h3>Your Account Permissions:</h3>
          <ul>
            <li>✅ Create and publish articles</li>
            <li>✅ Edit your profile</li>
            <li>✅ Comment on articles</li>
            ${canInsertAds ? '<li>✅ Insert advertisements in articles</li>' : '<li>❌ Insert advertisements (contact admin if needed)</li>'}
          </ul>
        </div>

        <p>You can now sign in to your account and start creating amazing content!</p>

        <div style="text-align: center;">
          <a href="${loginUrl}" class="button">Sign In to Your Account</a>
        </div>

        <p>Welcome to the Wikify Blog community! We're excited to see what you'll create.</p>

        <p>Best regards,<br>The Wikify Blog Team</p>
      </div>

      <div class="footer">
        <p>You're receiving this email because your Wikify Blog account status has been updated.</p>
      </div>
    </body>
    </html>
  `;
}

// Generate account rejection email HTML
export function generateRejectionEmail(username: string, displayName: string): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Account Registration Update - Wikify Blog</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #f8d7da;
          padding: 20px;
          text-align: center;
          border-radius: 8px 8px 0 0;
        }
        .content {
          background-color: #ffffff;
          padding: 30px;
          border: 1px solid #e9ecef;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 0 0 8px 8px;
          font-size: 14px;
          color: #6c757d;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #dc3545;
        }
        .info-box {
          background-color: #fff3cd;
          border-left: 4px solid #ffc107;
          padding: 15px;
          margin: 20px 0;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Wikify Blog</div>
        <h1>Registration Update</h1>
      </div>

      <div class="content">
        <p>Hello ${displayName || username},</p>

        <p>Thank you for your interest in joining Wikify Blog. After reviewing your registration, we regret to inform you that we cannot approve your account at this time.</p>

        <div class="info-box">
          <h3>What this means:</h3>
          <p>Your account registration has not been approved based on our current community guidelines and requirements.</p>
        </div>

        <p>This decision may be due to various factors including:</p>
        <ul>
          <li>Incomplete or insufficient registration information</li>
          <li>Content guidelines alignment</li>
          <li>Community standards requirements</li>
        </ul>

        <p>If you believe this decision was made in error or if you have additional information to share, please feel free to contact our support team for further assistance.</p>

        <p>Thank you for your understanding.</p>

        <p>Best regards,<br>The Wikify Blog Team</p>
      </div>

      <div class="footer">
        <p>You're receiving this email because you registered for an account on Wikify Blog.</p>
      </div>
    </body>
    </html>
  `;
}

// Send welcome email for new user registration
export async function sendWelcomeEmail(
  email: string,
  username: string,
  displayName: string
): Promise<boolean> {
  const htmlContent = generateWelcomeEmail(username, displayName);

  return await sendEmail({
    to: email,
    subject: 'Welcome to Wikify Blog - Account Pending Approval',
    html: htmlContent,
  });
}

// Send account approval email
export async function sendApprovalEmail(
  email: string,
  username: string,
  displayName: string,
  canInsertAds: boolean = false
): Promise<boolean> {
  const htmlContent = generateApprovalEmail(username, displayName, canInsertAds);

  return await sendEmail({
    to: email,
    subject: '🎉 Your Wikify Blog Account Has Been Approved!',
    html: htmlContent,
  });
}

// Send account rejection email
export async function sendRejectionEmail(
  email: string,
  username: string,
  displayName: string
): Promise<boolean> {
  const htmlContent = generateRejectionEmail(username, displayName);

  return await sendEmail({
    to: email,
    subject: 'Wikify Blog Registration Update',
    html: htmlContent,
  });
}

// Send password reset email
export async function sendPasswordResetEmail(
  email: string,
  username: string,
  resetToken: string
): Promise<boolean> {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const resetUrl = `${baseUrl}/auth/reset-password?token=${resetToken}`;

  const htmlContent = generatePasswordResetEmail(resetUrl, username);

  return await sendEmail({
    to: email,
    subject: 'Password Reset Request - Wikify Blog',
    html: htmlContent,
  });
}

// ==================== POST MODERATION EMAIL TEMPLATES ====================

// Generate post approved email HTML
export function generatePostApprovedEmail(
  username: string,
  postTitle: string,
  adminName: string,
  postUrl: string
): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Post Approved - Wikify Blog</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background: linear-gradient(135deg, #28a745, #20c997);
          padding: 30px 20px;
          text-align: center;
          border-radius: 8px 8px 0 0;
          color: white;
        }
        .content {
          background-color: #ffffff;
          padding: 30px;
          border: 1px solid #e9ecef;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 0 0 8px 8px;
          font-size: 14px;
          color: #6c757d;
        }
        .btn {
          display: inline-block;
          padding: 12px 24px;
          background: linear-gradient(135deg, #007bff, #0056b3);
          color: white;
          text-decoration: none;
          border-radius: 5px;
          margin: 15px 0;
          font-weight: bold;
        }
        .success-icon {
          font-size: 48px;
          margin-bottom: 15px;
        }
        .post-info {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          border-left: 4px solid #28a745;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="success-icon">🎉</div>
        <h1 style="margin: 0; font-size: 28px;">Post Approved!</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your content is now live</p>
      </div>

      <div class="content">
        <h2>Great news, ${username}!</h2>

        <p>We're excited to let you know that your post has been approved and is now live on Wikify Blog!</p>

        <div class="post-info">
          <h3 style="margin-top: 0; color: #28a745;">📝 Post Details</h3>
          <p><strong>Title:</strong> ${postTitle}</p>
          <p><strong>Approved by:</strong> ${adminName}</p>
          <p><strong>Status:</strong> <span style="color: #28a745; font-weight: bold;">✅ Published</span></p>
        </div>

        <p>Your post is now visible to all visitors and can be found in search results. Thank you for contributing quality content to our community!</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${postUrl}" class="btn">View Your Published Post</a>
        </div>

        <p><strong>What's next?</strong></p>
        <ul>
          <li>Share your post on social media to reach more readers</li>
          <li>Engage with comments from your readers</li>
          <li>Consider writing more great content!</li>
        </ul>

        <p>Keep up the excellent work!</p>

        <p>Best regards,<br>The Wikify Blog Team</p>
      </div>

      <div class="footer">
        <p>You're receiving this email because your post was moderated on Wikify Blog.</p>
        <p>© ${new Date().getFullYear()} Wikify Blog. All rights reserved.</p>
      </div>
    </body>
    </html>
  `;
}

// Generate post rejected email HTML
export function generatePostRejectedEmail(
  username: string,
  postTitle: string,
  adminName: string,
  rejectionReason: string,
  editUrl: string
): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Post Needs Revision - Wikify Blog</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background: linear-gradient(135deg, #ffc107, #fd7e14);
          padding: 30px 20px;
          text-align: center;
          border-radius: 8px 8px 0 0;
          color: white;
        }
        .content {
          background-color: #ffffff;
          padding: 30px;
          border: 1px solid #e9ecef;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 0 0 8px 8px;
          font-size: 14px;
          color: #6c757d;
        }
        .btn {
          display: inline-block;
          padding: 12px 24px;
          background: linear-gradient(135deg, #007bff, #0056b3);
          color: white;
          text-decoration: none;
          border-radius: 5px;
          margin: 15px 0;
          font-weight: bold;
        }
        .warning-icon {
          font-size: 48px;
          margin-bottom: 15px;
        }
        .post-info {
          background-color: #fff3cd;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          border-left: 4px solid #ffc107;
        }
        .reason-box {
          background-color: #f8d7da;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          border-left: 4px solid #dc3545;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="warning-icon">📝</div>
        <h1 style="margin: 0; font-size: 28px;">Post Needs Revision</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your post requires some changes</p>
      </div>

      <div class="content">
        <h2>Hello ${username},</h2>

        <p>Thank you for submitting your post to Wikify Blog. After review, we've identified some areas that need attention before we can publish your content.</p>

        <div class="post-info">
          <h3 style="margin-top: 0; color: #856404;">📝 Post Details</h3>
          <p><strong>Title:</strong> ${postTitle}</p>
          <p><strong>Reviewed by:</strong> ${adminName}</p>
          <p><strong>Status:</strong> <span style="color: #856404; font-weight: bold;">⏳ Needs Revision</span></p>
        </div>

        <div class="reason-box">
          <h3 style="margin-top: 0; color: #721c24;">📋 Feedback from ${adminName}</h3>
          <p style="margin-bottom: 0;"><strong>Reason for revision:</strong></p>
          <p style="font-style: italic; margin-top: 10px;">"${rejectionReason}"</p>
        </div>

        <p><strong>What to do next:</strong></p>
        <ol>
          <li>Review the feedback provided above</li>
          <li>Edit your post to address the mentioned issues</li>
          <li>Resubmit your post for review</li>
        </ol>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${editUrl}" class="btn">Edit Your Post</a>
        </div>

        <p>Don't worry - this is a normal part of the content review process. We're here to help you create the best possible content for our readers!</p>

        <p>If you have any questions about the feedback or need assistance with your revisions, please don't hesitate to contact our support team.</p>

        <p>Best regards,<br>The Wikify Blog Team</p>
      </div>

      <div class="footer">
        <p>You're receiving this email because your post was moderated on Wikify Blog.</p>
        <p>© ${new Date().getFullYear()} Wikify Blog. All rights reserved.</p>
      </div>
    </body>
    </html>
  `;
}

// Generate post deleted email HTML
export function generatePostDeletedEmail(
  username: string,
  postTitle: string,
  adminName: string,
  deletionReason: string
): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Post Removed - Wikify Blog</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background: linear-gradient(135deg, #dc3545, #c82333);
          padding: 30px 20px;
          text-align: center;
          border-radius: 8px 8px 0 0;
          color: white;
        }
        .content {
          background-color: #ffffff;
          padding: 30px;
          border: 1px solid #e9ecef;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px;
          text-align: center;
          border-radius: 0 0 8px 8px;
          font-size: 14px;
          color: #6c757d;
        }
        .btn {
          display: inline-block;
          padding: 12px 24px;
          background: linear-gradient(135deg, #007bff, #0056b3);
          color: white;
          text-decoration: none;
          border-radius: 5px;
          margin: 15px 0;
          font-weight: bold;
        }
        .delete-icon {
          font-size: 48px;
          margin-bottom: 15px;
        }
        .post-info {
          background-color: #f8d7da;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          border-left: 4px solid #dc3545;
        }
        .reason-box {
          background-color: #f8d7da;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          border-left: 4px solid #dc3545;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="delete-icon">🗑️</div>
        <h1 style="margin: 0; font-size: 28px;">Post Removed</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your post has been removed from the site</p>
      </div>

      <div class="content">
        <h2>Hello ${username},</h2>

        <p>We're writing to inform you that your post has been removed from Wikify Blog after review by our moderation team.</p>

        <div class="post-info">
          <h3 style="margin-top: 0; color: #721c24;">📝 Post Details</h3>
          <p><strong>Title:</strong> ${postTitle}</p>
          <p><strong>Removed by:</strong> ${adminName}</p>
          <p><strong>Status:</strong> <span style="color: #721c24; font-weight: bold;">❌ Removed</span></p>
        </div>

        <div class="reason-box">
          <h3 style="margin-top: 0; color: #721c24;">📋 Reason for Removal</h3>
          <p style="font-style: italic; margin: 0;">"${deletionReason}"</p>
        </div>

        <p><strong>What this means:</strong></p>
        <ul>
          <li>Your post is no longer visible on the website</li>
          <li>The content has been permanently removed from our system</li>
          <li>This action cannot be undone</li>
        </ul>

        <p>If you believe this removal was made in error or if you have questions about our content policies, please contact our support team for clarification.</p>

        <p>We encourage you to review our community guidelines and content policies before submitting future posts.</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/dashboard/new-post" class="btn">Create New Post</a>
        </div>

        <p>Thank you for your understanding.</p>

        <p>Best regards,<br>The Wikify Blog Team</p>
      </div>

      <div class="footer">
        <p>You're receiving this email because your post was moderated on Wikify Blog.</p>
        <p>© ${new Date().getFullYear()} Wikify Blog. All rights reserved.</p>
      </div>
    </body>
    </html>
  `;
}

// ==================== POST MODERATION EMAIL FUNCTIONS ====================

// Send post approved email
export async function sendPostApprovedEmail(
  email: string,
  username: string,
  postTitle: string,
  adminName: string,
  postSlug: string
): Promise<boolean> {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const postUrl = `${baseUrl}/posts/${postSlug}`;

  const htmlContent = generatePostApprovedEmail(username, postTitle, adminName, postUrl);

  return await sendEmail({
    to: email,
    subject: `🎉 Your post "${postTitle}" has been approved!`,
    html: htmlContent,
  });
}

// Send post rejected email
export async function sendPostRejectedEmail(
  email: string,
  username: string,
  postTitle: string,
  adminName: string,
  rejectionReason: string,
  postId: number
): Promise<boolean> {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const editUrl = `${baseUrl}/dashboard/edit-post/${postId}`;

  const htmlContent = generatePostRejectedEmail(username, postTitle, adminName, rejectionReason, editUrl);

  return await sendEmail({
    to: email,
    subject: `📝 Your post "${postTitle}" needs revision`,
    html: htmlContent,
  });
}

// Send post deleted email
export async function sendPostDeletedEmail(
  email: string,
  username: string,
  postTitle: string,
  adminName: string,
  deletionReason: string
): Promise<boolean> {
  const htmlContent = generatePostDeletedEmail(username, postTitle, adminName, deletionReason);

  return await sendEmail({
    to: email,
    subject: `🗑️ Your post "${postTitle}" has been removed`,
    html: htmlContent,
  });
}

// ==================== USER APPROVAL EMAIL FUNCTIONS ====================

// Send user approved email (enhanced)
export async function sendUserApprovedEmail(
  email: string,
  username: string,
  displayName: string,
  adminName: string,
  canInsertAds: boolean = false
): Promise<boolean> {
  const htmlContent = generateApprovalEmail(username, displayName, canInsertAds);

  return await sendEmail({
    to: email,
    subject: `🎉 Your Wikify Blog account has been approved by ${adminName}!`,
    html: htmlContent,
  });
}

// Send user rejected email (enhanced)
export async function sendUserRejectedEmail(
  email: string,
  username: string,
  displayName: string,
  adminName: string,
  rejectionReason: string
): Promise<boolean> {
  const htmlContent = generateRejectionEmail(username, displayName);

  return await sendEmail({
    to: email,
    subject: `Wikify Blog Registration Update from ${adminName}`,
    html: htmlContent,
  });
}
