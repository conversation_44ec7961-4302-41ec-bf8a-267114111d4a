import { db } from '@/lib/db';
import { notifications, users } from '@/lib/db/schema';
import { eq, and, count } from 'drizzle-orm';
import {
  sendPostApprovedEmail,
  sendPostRejectedEmail,
  sendPostDeletedEmail,
  sendUserApprovedEmail,
  sendUserRejectedEmail
} from './email';

export interface CreateNotificationParams {
  userId: number;
  type: 'post_edited' | 'post_deleted' | 'post_approved' | 'post_rejected' | 'user_approved' | 'user_rejected';
  title: string;
  message: string;
  postId?: number;
  postTitle?: string;
  adminId: number;
  adminName: string;
  rejectionReason?: string;
  sendEmail?: boolean; // Flag to control email sending
}

/**
 * Create a notification for a user
 */
export async function createNotification(params: CreateNotificationParams): Promise<boolean> {
  try {
    // For rejected posts, we can store the rejection reason in the message
    // or we could extend the notification schema to include it separately
    let message = params.message;
    if (params.type === 'post_rejected' && params.rejectionReason) {
      // The rejection reason is already included in the message
      message = params.message;
    }

    await db
      .insert(notifications)
      .values({
        user_id: params.userId,
        type: params.type,
        title: params.title,
        message: message,
        post_id: params.postId || null,
        post_title: params.postTitle || null,
        admin_id: params.adminId,
        admin_name: params.adminName,
        is_read: false,
        created_at: new Date(),
      });

    return true;
  } catch (error) {
    console.error('Error creating notification:', error);
    return false;
  }
}

/**
 * Create notification when admin edits a user's post
 */
export async function createPostEditedNotification(
  userId: number,
  postId: number,
  postTitle: string,
  adminId: number,
  adminName: string
): Promise<boolean> {
  return createNotification({
    userId,
    type: 'post_edited',
    title: 'Your post has been edited',
    message: `An admin (${adminName}) has edited your post "${postTitle}". You can review the changes in your dashboard.`,
    postId,
    postTitle,
    adminId,
    adminName,
  });
}



/**
 * Create notification when admin approves a user's post (with email)
 */
export async function createPostApprovedNotification(
  userId: number,
  postId: number,
  postTitle: string,
  adminId: number,
  adminName: string,
  postSlug?: string
): Promise<boolean> {
  try {
    // Create in-app notification
    const notificationSuccess = await createNotification({
      userId,
      type: 'post_approved',
      title: 'Your post has been approved',
      message: `Great news! Your post "${postTitle}" has been approved by ${adminName} and is now live on the site.`,
      postId,
      postTitle,
      adminId,
      adminName,
    });

    // Send email notification
    try {
      // Get user email
      const user = await db
        .select({
          user_email: users.user_email,
          user_login: users.user_login,
          display_name: users.display_name
        })
        .from(users)
        .where(eq(users.ID, userId))
        .limit(1);

      if (user.length > 0 && user[0].user_email) {
        await sendPostApprovedEmail(
          user[0].user_email,
          user[0].display_name || user[0].user_login,
          postTitle,
          adminName,
          postSlug || `post-${postId}`
        );
      }
    } catch (emailError) {
      console.error('Failed to send post approved email:', emailError);
      // Don't fail the notification if email fails
    }

    return notificationSuccess;
  } catch (error) {
    console.error('Error creating post approved notification:', error);
    return false;
  }
}

/**
 * Create notification when admin rejects a user's post (with email)
 */
export async function createPostRejectedNotification(
  userId: number,
  postId: number,
  postTitle: string,
  adminId: number,
  adminName: string,
  rejectionReason?: string
): Promise<boolean> {
  try {
    // Create in-app notification
    const notificationSuccess = await createNotification({
      userId,
      type: 'post_rejected',
      title: 'Your post needs revision',
      message: `Your post "${postTitle}" was not approved. Reason: ${rejectionReason}. You can edit and resubmit your post from your dashboard.`,
      postId,
      postTitle,
      adminId,
      adminName,
      rejectionReason,
    });

    // Send email notification
    try {
      // Get user email
      const user = await db
        .select({
          user_email: users.user_email,
          user_login: users.user_login,
          display_name: users.display_name
        })
        .from(users)
        .where(eq(users.ID, userId))
        .limit(1);

      if (user.length > 0 && user[0].user_email) {
        await sendPostRejectedEmail(
          user[0].user_email,
          user[0].display_name || user[0].user_login,
          postTitle,
          adminName,
          rejectionReason || 'No specific reason provided',
          postId
        );
      }
    } catch (emailError) {
      console.error('Failed to send post rejected email:', emailError);
      // Don't fail the notification if email fails
    }

    return notificationSuccess;
  } catch (error) {
    console.error('Error creating post rejected notification:', error);
    return false;
  }
}

/**
 * Create notification when admin deletes a user's post (with email)
 */
export async function createPostDeletedNotification(
  userId: number,
  postId: number,
  postTitle: string,
  adminId: number,
  adminName: string,
  deletionReason: string
): Promise<boolean> {
  try {
    // Create in-app notification
    const notificationSuccess = await createNotification({
      userId,
      type: 'post_deleted',
      title: 'Your post has been removed',
      message: `Your post "${postTitle}" has been removed by ${adminName}. Reason: ${deletionReason}`,
      postId,
      postTitle,
      adminId,
      adminName,
      rejectionReason: deletionReason,
    });

    // Send email notification
    try {
      // Get user email
      const user = await db
        .select({
          user_email: users.user_email,
          user_login: users.user_login,
          display_name: users.display_name
        })
        .from(users)
        .where(eq(users.ID, userId))
        .limit(1);

      if (user.length > 0 && user[0].user_email) {
        await sendPostDeletedEmail(
          user[0].user_email,
          user[0].display_name || user[0].user_login,
          postTitle,
          adminName,
          deletionReason
        );
      }
    } catch (emailError) {
      console.error('Failed to send post deleted email:', emailError);
      // Don't fail the notification if email fails
    }

    return notificationSuccess;
  } catch (error) {
    console.error('Error creating post deleted notification:', error);
    return false;
  }
}

/**
 * Create notification when admin approves a user account (with email)
 */
export async function createUserApprovedNotification(
  userId: number,
  adminId: number,
  adminName: string,
  canInsertAds: boolean = false
): Promise<boolean> {
  try {
    // Create in-app notification
    const notificationSuccess = await createNotification({
      userId,
      type: 'user_approved',
      title: 'Your account has been approved',
      message: `Great news! Your account has been approved by ${adminName}. You can now start creating and publishing content.`,
      adminId,
      adminName,
    });

    // Send email notification
    try {
      // Get user email
      const user = await db
        .select({
          user_email: users.user_email,
          user_login: users.user_login,
          display_name: users.display_name
        })
        .from(users)
        .where(eq(users.ID, userId))
        .limit(1);

      if (user.length > 0 && user[0].user_email) {
        await sendUserApprovedEmail(
          user[0].user_email,
          user[0].user_login,
          user[0].display_name || user[0].user_login,
          adminName,
          canInsertAds
        );
      }
    } catch (emailError) {
      console.error('Failed to send user approved email:', emailError);
      // Don't fail the notification if email fails
    }

    return notificationSuccess;
  } catch (error) {
    console.error('Error creating user approved notification:', error);
    return false;
  }
}

/**
 * Create notification when admin rejects a user account (with email)
 */
export async function createUserRejectedNotification(
  userId: number,
  adminId: number,
  adminName: string,
  rejectionReason: string
): Promise<boolean> {
  try {
    // Create in-app notification
    const notificationSuccess = await createNotification({
      userId,
      type: 'user_rejected',
      title: 'Your account registration was not approved',
      message: `Your account registration was not approved by ${adminName}. Reason: ${rejectionReason}`,
      adminId,
      adminName,
      rejectionReason,
    });

    // Send email notification
    try {
      // Get user email
      const user = await db
        .select({
          user_email: users.user_email,
          user_login: users.user_login,
          display_name: users.display_name
        })
        .from(users)
        .where(eq(users.ID, userId))
        .limit(1);

      if (user.length > 0 && user[0].user_email) {
        await sendUserRejectedEmail(
          user[0].user_email,
          user[0].user_login,
          user[0].display_name || user[0].user_login,
          adminName,
          rejectionReason
        );
      }
    } catch (emailError) {
      console.error('Failed to send user rejected email:', emailError);
      // Don't fail the notification if email fails
    }

    return notificationSuccess;
  } catch (error) {
    console.error('Error creating user rejected notification:', error);
    return false;
  }
}

/**
 * Get unread notification count for a user
 */
export async function getUnreadNotificationCount(userId: number): Promise<number> {
  try {
    const result = await db
      .select({ count: count() })
      .from(notifications)
      .where(
        and(
          eq(notifications.user_id, userId),
          eq(notifications.is_read, false)
        )
      );

    return result[0]?.count || 0;
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    return 0;
  }
}
